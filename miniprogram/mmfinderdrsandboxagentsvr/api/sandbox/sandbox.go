// Package sandbox 定义相关的接口
package sandbox

import (
	"net/http"

	"git.woa.com/wego/wxg/weenv"
	"git.woa.com/wxg-mmfinderluban/mmfinderluban/miniprogram/mmfinderdrsandboxbase/model/api/base"
	"git.woa.com/wxg-mmfinderluban/mmfinderluban/miniprogram/mmfinderdrsandboxbase/util"
	"github.com/gorilla/mux"

	sandbox_api_model "mmfinderdrsandboxagentsvr/model/api/sandbox"
	sandbox_service "mmfinderdrsandboxagentsvr/service/sandbox"
)

func authCode(w http.ResponseWriter, r *http.Request) {
	// 0. 处理 context，request 和 response
	ctx := r.Context()
	uin := r.URL.Query().Get("uin")
	resp := &base.Resp[sandbox_api_model.AuthCodeRespData]{}
	w.Header().Set("Content-Type", "application/json")
	// 1. 处理业务逻辑
	data, err := sandbox_service.SandboxOnlineObj.GetAuthCode(ctx, uin)
	resp.Data = &data
	util.CommonProcessAPIResponse(w, r, resp, err, "获取认证码")
}

func cdpProxy(w http.ResponseWriter, r *http.Request) {
	util.CommonProcessAPI(
		w,
		r,
		&sandbox_api_model.CDPProxyReq{},
		sandbox_service.SandboxOnlineObj.ExecuteInstruction,
		"执行指令",
	)
}

func userCli(w http.ResponseWriter, r *http.Request) {
	// 0. 处理 context，request 和 response
	ctx := r.Context()
	resp := &base.Resp[sandbox_api_model.UserCliRespData]{
		Data: &sandbox_api_model.UserCliRespData{
			ID: -1,
			IP: weenv.GetInnerIp(),
		},
	}
	w.Header().Set("Content-Type", "application/json")
	// 1. 处理业务逻辑
	_, _ = sandbox_service.SandboxOnlineObj.Logout(ctx)
	util.CommonProcessAPIResponse(w, r, resp, nil, "")
}

// Init 初始化
func Init(router *mux.Router) {
	router.HandleFunc("/v1/sandbox/auth_code", authCode).Methods("GET")
	router.HandleFunc("/v1/sandbox/cdp_proxy/{userId}", cdpProxy).Methods("POST")
	router.HandleFunc("/v1/sandbox/user_cli", userCli).Methods("GET")

	// 初始化沙箱标注相关的路由
	router.HandleFunc("/v1/sandbox/annotations/create", createSandbox).Methods("POST")
	router.HandleFunc("/v1/sandbox/annotations/action", sandboxAction).Methods("POST")
	router.HandleFunc("/v1/sandbox/annotations/kill", killSandbox).Methods("POST")
	router.HandleFunc("/v1/sandbox/annotations/submit", submitAnnotation).Methods("POST")
	router.HandleFunc("/v1/sandbox/annotations/delete", deleteAnnotation).Methods("POST")
	router.HandleFunc("/v1/sandbox/annotations/eval", evalAnnotation).Methods("POST")
	router.HandleFunc("/v1/sandbox/annotations/ready", readyAnnotation).Methods("POST")
	router.HandleFunc("/v1/sandbox/annotations/delete_operation", deleteAnnotationOperation).Methods("POST")
	router.HandleFunc("/v1/sandbox/annotations/update_operation", updateAnnotationOperation).Methods("POST")
	router.HandleFunc("/v1/sandbox/annotations/update", updateAnnotation).Methods("POST")
	router.HandleFunc("/v1/sandbox/annotations", getAnnotations).Methods("GET")
}
