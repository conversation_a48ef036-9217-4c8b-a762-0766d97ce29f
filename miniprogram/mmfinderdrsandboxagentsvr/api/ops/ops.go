// Package ops 定义相关的接口
package ops

import (
	"net/http"

	"git.woa.com/wxg-mmfinderluban/mmfinderluban/miniprogram/mmfinderdrsandboxbase/model/api/base"
	"git.woa.com/wxg-mmfinderluban/mmfinderluban/miniprogram/mmfinderdrsandboxbase/util"
	"github.com/gorilla/mux"
)

func health(w http.ResponseWriter, r *http.Request) {
	// 0. 处理 context，request 和 response
	resp := &base.Resp[any]{}
	w.Header().Set("Content-Type", "application/json")
	// 1. 处理业务逻辑
	util.CommonProcessAPIResponse(w, r, resp, nil, "")
}

// Init 初始化
func Init(router *mux.Router) {
	router.HandleFunc("/v1/ops/health", health).Methods("GET")
}
