// Package api 初始化所有接口
package api

import (
	"encoding/json"
	"net/http"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/filter"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/server"
	base_api "git.woa.com/wxg-mmfinderluban/mmfinderluban/miniprogram/mmfinderdrsandboxbase/api"
	"git.woa.com/wxg-mmfinderluban/mmfinderluban/miniprogram/mmfinderdrsandboxbase/util"
	"github.com/gorilla/mux"

	agent_api "mmfinderdrsandboxagentsvr/api/agent"
	ops_api "mmfinderdrsandboxagentsvr/api/ops"
	sandbox_api "mmfinderdrsandboxagentsvr/api/sandbox"
	agent_service "mmfinderdrsandboxagentsvr/service/agent"
	sandbox_service "mmfinderdrsandboxagentsvr/service/sandbox"
)

func init() {
	// 拦截器
	filter.Register("TrpcHeaderMiddleware", base_api.TrpcHeaderMiddleware, nil)
}

// Init 初始化
func Init(trpcServer *server.Server) {
	// 初始化 service
	agent_service.Init()
	sandbox_service.InitSandboxOnline()
	// 配置自定义错误处理器
	thttp.DefaultServerCodec.ErrHandler = func(w http.ResponseWriter, r *http.Request, e *errs.Error) {
		ctx := r.Context()
		w.Header().Set("Content-Type", "application/json")
		// tRPC 错误码到 HTTP 状态码的映射
		httpStatus := util.MapTRPCErrorToHTTP(e.Code)
		w.WriteHeader(httpStatus)
		// 响应体
		resp := map[string]interface{}{
			"code":    e.Code,
			"message": e.Msg,
			"type":    util.GetErrorType(e.Code),
		}
		// 写入 response
		if err := json.NewEncoder(w).Encode(resp); err != nil {
			log.WithContext(
				ctx,
				log.Field{Key: "path", Value: r.URL.Path},
				log.Field{Key: "raw_query", Value: r.URL.RawQuery},
				log.Field{Key: "error", Value: err},
			).Error("写入 response 失败")
		}
	}
	// 创建 mux 路由器
	router := mux.NewRouter()
	// 注册路由
	agent_api.Init(router)
	sandbox_api.Init(router)
	ops_api.Init(router)
	// 将 mux 路由器注册到 tRPC HTTP 服务
	thttp.RegisterNoProtocolServiceMux(
		trpcServer.Service("trpc.mmfinderdrsandbox.mmfinderdrsandboxagentsvr.server"),
		router,
	)
}

// Exit 优雅退出程序
func Exit() {
	// sandbox_service.SandboxOnlineObj.Exit()
}
