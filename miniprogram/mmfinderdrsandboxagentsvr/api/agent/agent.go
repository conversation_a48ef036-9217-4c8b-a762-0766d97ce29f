// Package agent 定义相关的接口
package agent

import (
	"encoding/json"
	"net/http"
	"time"

	"git.woa.com/wxg-mmfinderluban/mmfinderluban/miniprogram/mmfinderdrsandboxbase/errors"
	"git.woa.com/wxg-mmfinderluban/mmfinderluban/miniprogram/mmfinderdrsandboxbase/model/api/base"
	"git.woa.com/wxg-mmfinderluban/mmfinderluban/miniprogram/mmfinderdrsandboxbase/util"
	"github.com/gorilla/mux"

	agent_api_model "mmfinderdrsandboxagentsvr/model/api/agent"
	agent_service "mmfinderdrsandboxagentsvr/service/agent"
)

func agentRun(w http.ResponseWriter, r *http.Request) {
	// 0. 处理 context，request 和 response
	ctx := r.Context()
	req := &agent_api_model.RunReq{}
	req.Position = &agent_api_model.PositionInfo{
		Latitude:  -1,
		Longitude: -1,
	}
	if err := json.NewDecoder(r.Body).Decode(req); err != nil || req.AppID == "" || req.AuthCode == "" {
		util.WriteHTTPResponse(
			w,
			errors.BadRequest.Code,
			"参数非法或参数 AppID 或 AuthCode 为空",
			http.StatusBadRequest,
		)
		return
	}
	resp := &base.Resp[agent_api_model.BaseRespData]{
		Data: &agent_api_model.BaseRespData{},
	}
	w.Header().Set("Content-Type", "application/json")
	// 1. 处理业务逻辑
	if err := util.ExecuteFunctionInQueueWithTimeout(time.Second*45, func() {
		args, _ := req.APIArgs(r)
		resp, _ = agent_service.Run(
			ctx, req.AuthCode, req.Position, req.SkipShareURLData, args,
		)
	}); err != nil {
		// 如果被锁 block 住太久则直接返回
		util.WriteHTTPResponse(
			w,
			errors.InternalError.Code,
			"系统繁忙，请稍后再试",
			http.StatusTooManyRequests,
		)
		return
	}
	util.CommonProcessAPIResponse(w, r, resp, nil, "")
}

func agentRunHeadless(w http.ResponseWriter, r *http.Request) {
	// 0. 处理 context，request 和 response
	ctx := r.Context()
	req := &agent_api_model.BaseReq{}
	if err := json.NewDecoder(r.Body).Decode(req); err != nil || req.AppID == "" {
		util.WriteHTTPResponse(
			w,
			errors.BadRequest.Code,
			"参数非法或参数 AppID 为空",
			http.StatusBadRequest,
		)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	// 1. 处理业务逻辑
	// 无头不应该有锁控制
	args, _ := req.APIArgs(r)
	resp, _ := agent_service.Run(ctx, "", nil, true, args)
	util.CommonProcessAPIResponse(w, r, resp, nil, "")
}

// Init 注册 agent 相关的路由
func Init(router *mux.Router) {
	router.HandleFunc("/v1/agent/run", agentRun).Methods("POST")
	router.HandleFunc("/v1/agent/run_headless", agentRunHeadless).Methods("POST")
}
