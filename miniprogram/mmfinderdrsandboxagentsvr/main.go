// Package main
package main

import (
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.woa.com/wxg-mmfinderluban/mmfinderluban/miniprogram/mmfinderdrsandboxbase/middleware/tracer"
	_ "git.woa.com/wxg-mmfinderluban/mmfinderluban/miniprogram/mmfinderdrsandboxbase/middleware/wecube"
	_ "github.com/lib/pq"

	"mmfinderdrsandboxagentsvr/api"
	"mmfinderdrsandboxagentsvr/middleware/db"
)

func main() {
	trpcServer := trpc.NewServer()
	// 初始化顺序：配置和环境 -> 中间件 -> api 创建 tRPC 服务
	db.Init()
	api.Init(trpcServer)
	// 优雅退出：注册 afterShutdownHooks：在服务器启动关闭之后，且所有 service 执行关闭之后，执行一组预定义的钩子函数。
	// 详见 https://iwiki.woa.com/p/4012293463
	trpcServer.RegisterOnShutdown(func() {
		log.Info("接收到 SIGTERM 或 SIGINT 系统信号，退出程序中")
		api.Exit()
		log.Info("退出程序完成")
	})
	// 启动服务
	if err := trpcServer.Serve(); err != nil {
		log.Fatalf("failed to start server: %v", err)
	}
}
