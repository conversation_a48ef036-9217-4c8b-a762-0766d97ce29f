// Package agent 定义相关的接口类型
package agent

import (
	"encoding/json"
	"net/http"
)

// PositionInfo 定义相关的接口类型
type PositionInfo struct {
	Latitude  float64 `json:"latitude"`  // 纬度，默认值 -1 表示无效值
	Longitude float64 `json:"longitude"` // 经度，默认指 -1 表示无效值
}

// BaseReq 定义相关的接口类型
type BaseReq struct {
	AppEntryURL                                             string `json:"app_entry_url,omitempty"`
	AppID                                                   string `json:"app_id,omitempty"`
	AppName                                                 string `json:"app_name,omitempty"`
	BaseURL                                                 string `json:"base_url,omitempty"`
	FromUsername                                            string `json:"from_username,omitempty"`
	HeadlessMode                                            string `json:"headless_mode,omitempty"`
	Instruction                                             string `json:"instruction,omitempty"`
	ModelName                                               string `json:"model_name,omitempty"`
	Namespace                                               string `json:"namespace,omitempty"`
	Prompt                                                  string `json:"prompt,omitempty"`
	PromptVLT                                               string `json:"prompt_vlt,omitempty"`
	PromptVLTV1                                             string `json:"prompt_vlt_v1,omitempty"`
	RagBaseURL                                              string `json:"rag_base_url,omitempty"`
	RagExtraConfig                                          string `json:"rag_extra_config,omitempty"`
	RawQuery                                                string `json:"raw_query,omitempty"`
	RunMode                                                 string `json:"run_mode,omitempty"`
	SandboxCheckScreenshotLoadingStatusMaxRetryCount        string `json:"sandbox_check_screenshot_loading_status_max_retry_count,omitempty"`          //nolint:lll
	SandboxCheckScreenshotLoadingStatusMaxRetryCountForBack string `json:"sandbox_check_screenshot_loading_status_max_retry_count_for_back,omitempty"` //nolint:lll
	SandboxCloseApplet                                      string `json:"sandbox_close_applet,omitempty"`
	SandboxCloseAppletWithSessionAlive                      string `json:"sandbox_close_applet_with_session_alive,omitempty"` //nolint:lll
	SandboxIsAsyncActionResult                              string `json:"sandbox_is_async_action_result,omitempty"`
	SandboxLinkType                                         string `json:"sandbox_link_type,omitempty"`
	SandboxSkipLaunchApplet                                 string `json:"sandbox_skip_launch_applet,omitempty"`
	SandboxSleepSecondsBeforeScreenshot                     string `json:"sandbox_sleep_seconds_before_screenshot,omitempty"` //nolint:lll
	SpecialAppIDList                                        string `json:"special_app_id_list,omitempty"`
	SpecialStr1                                             string `json:"special_str_1,omitempty"`
	SpecialStr2                                             string `json:"special_str_2,omitempty"`
	UIN                                                     string `json:"uin,omitempty"`
	Username                                                string `json:"username,omitempty"`
	VLTBaseURL                                              string `json:"vlt_base_url,omitempty"`
	VLTBaseURLV1                                            string `json:"vlt_base_url_v1,omitempty"`
	VLTModelName                                            string `json:"vlt_model_name,omitempty"`
	VLTModelNameV1                                          string `json:"vlt_model_name_v1,omitempty"`
}

// APIArgs 获取调用推理服务的 API 参数
func (req *BaseReq) APIArgs(r *http.Request) (map[string]string, error) {
	data, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	var res map[string]string
	if err := json.Unmarshal(data, &res); err != nil {
		return nil, err
	}
	return res, nil
}

// RunReq 定义相关的接口类型
type RunReq struct {
	BaseReq
	AuthCode         string        `json:"auth_code"`
	Position         *PositionInfo `json:"position"`
	SkipShareURLData bool          `json:"skip_share_url_data"`
}

// BaseRespData 定义相关的接口类型
type BaseRespData struct {
	DebugStr          string   `json:"debug_str"`
	LongImgURL        string   `json:"long_img_url"`
	LongUniqueImgURL  string   `json:"long_unique_img_url"`
	ScreenshotImgURLs []string `json:"screenshot_img_urls"`
	ShareURL          string   `json:"share_url"`
	Thinking          []string `json:"thinking"`
}
