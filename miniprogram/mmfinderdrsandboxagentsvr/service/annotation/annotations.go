// Package annotation 沙箱标注服务
package annotation

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/wxg-mmfinderluban/mmfinderluban/miniprogram/mmfinderdrsandboxbase/errors"
	"git.woa.com/wxg-mmfinderluban/mmfinderluban/miniprogram/mmfinderdrsandboxbase/model/api/base"
	"go.opentelemetry.io/otel/trace"

	"mmfinderdrsandboxagentsvr/middleware/db"
	sandbox_api_model "mmfinderdrsandboxagentsvr/model/api/sandbox"
	agent_service_model "mmfinderdrsandboxagentsvr/model/service/agent"
)

// CreateSandbox 创建沙箱
func CreateSandbox(
	ctx context.Context,
	req *sandbox_api_model.CreateSandboxReq,
) (*base.Resp[sandbox_api_model.CreateSandboxRespData], error) {
	currentSpanTraceID := ""
	span := trace.SpanContextFromContext(ctx)
	if span.IsValid() {
		currentSpanTraceID = span.TraceID().String()
	} else {
		return nil, errors.BadRequest.WithMessage("current span is invalid")
	}
	// 启动小程序页面
	res, err := LaunchAppletAction(ctx, req.UserID, req.AppID, req.AppEntryURL)
	if err != nil {
		return nil, err
	}
	// 获取当前页面信息
	actionResp, err := getCurrentPageInfo(ctx, res)
	if err != nil {
		return nil, err
	}
	// 写入 annotation_session
	err = db.CreateAnnotationSession(currentSpanTraceID, currentSpanTraceID, req.AppID, req.UserID, req.RTX)
	if err != nil {
		log.WithContext(ctx, log.Field{Key: "error", Value: err}).Error("创建标注会话失败")
		return nil, err
	}
	// 返回响应
	return &base.Resp[sandbox_api_model.CreateSandboxRespData]{
		Message: "success",
		Data: &sandbox_api_model.CreateSandboxRespData{
			URL:              actionResp.URL,
			DomXML:           actionResp.DomXML,
			AllElementsRects: actionResp.AllElementsRects,
			ExtraInfo:        actionResp.ExtraInfo,
			ScreenWidth:      actionResp.ScreenWidth,
			ScreenHeight:     actionResp.ScreenHeight,
			TargetID:         currentSpanTraceID,
		},
	}, nil
}

// ExecuteSandboxAction 沙箱交互步骤
func ExecuteSandboxAction(
	ctx context.Context,
	req *sandbox_api_model.ActionReq,
) (*base.Resp[sandbox_api_model.ActionRespData], error) {
	if err := checkTargetID(ctx, req.TargetID, req.UserID, req.AppID); err != nil {
		return nil, err
	}
	// 根据操作类型执行不同的动作
	res, err := executeAction(ctx, req)
	if err != nil {
		return nil, err
	}
	if res == nil {
		return nil, fmt.Errorf("执行操作失败")
	}
	// 获取当前页面信息
	actionResp, err := getCurrentPageInfo(ctx, res)
	if err != nil {
		return nil, err
	}
	// 返回响应
	return &base.Resp[sandbox_api_model.ActionRespData]{
		Message: "success",
		Data:    actionResp,
	}, nil
}

// executeAction 执行具体的沙箱操作
func executeAction(ctx context.Context, req *sandbox_api_model.ActionReq) (
	*agent_service_model.PythonResult,
	error,
) {
	switch req.Type {
	case "click":
		return executeClickAction(ctx, req)
	case "scroll":
		return executeScrollAction(ctx, req)
	case "search":
		return executeSearchAction(ctx, req)
	case "wait":
		return executeWaitAction(ctx, req)
	case "back":
		return executeBackAction(ctx, req)
	default:
		return nil, fmt.Errorf("不支持的操作类型: %s", req.Type)
	}
}

// executeClickAction 执行点击操作
func executeClickAction(
	ctx context.Context,
	req *sandbox_api_model.ActionReq,
) (*agent_service_model.PythonResult, error) {
	if (req.MarkConfig == nil && req.Xpath == "") || req.Screen == nil {
		return nil, fmt.Errorf("点击操作需要提供markConfig|Xpath和screen参数")
	}
	var x float64
	var y float64
	if req.Xpath == "" {
		// 计算实际点击坐标
		x = (req.MarkConfig.X + req.MarkConfig.Width/2) * float64(req.Screen.ScreenWidth)
		y = (req.MarkConfig.Y + req.MarkConfig.Height/2) * float64(req.Screen.ScreenHeight)
	}
	res, err := ClickAction(ctx, req.UserID, req.AppID, int64(x), int64(y), req.Xpath)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// executeWaitAction 执行等待操作
func executeWaitAction(
	ctx context.Context,
	req *sandbox_api_model.ActionReq,
) (*agent_service_model.PythonResult, error) {
	res, err := WaitAction(ctx, req.UserID, req.AppID)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// executeBackAction 执行返回操作
func executeBackAction(
	ctx context.Context,
	req *sandbox_api_model.ActionReq,
) (*agent_service_model.PythonResult, error) {
	res, err := BackAction(ctx, req.UserID, req.AppID)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// executeScrollAction 执行滚动操作
func executeScrollAction(
	ctx context.Context,
	req *sandbox_api_model.ActionReq,
) (*agent_service_model.PythonResult, error) {
	if req.Direction == "" {
		return nil, fmt.Errorf("滚动操作需要提供direction参数")
	}
	// 设置默认坐标和滚动距离
	x := float64(100)
	y := float64(100)
	if req.MarkConfig != nil {
		x = (req.MarkConfig.X + req.MarkConfig.Width/2) * float64(req.Screen.ScreenWidth)
		y = (req.MarkConfig.Y + req.MarkConfig.Height/2) * float64(req.Screen.ScreenHeight)
	} else if req.Screen != nil {
		x = 0.5 * float64(req.Screen.ScreenWidth)
		y = 0.5 * float64(req.Screen.ScreenHeight)
	}

	deltaX := 0.0
	deltaY := 0.0
	// 根据方向调整滚动像素值
	switch req.Direction {
	case "up":
		deltaY = float64(req.Length)
	case "down":
		deltaY = -float64(req.Length)
	case "left":
		deltaX = float64(req.Length)
	case "right":
		deltaX = -float64(req.Length)
	}

	res, err := ScrollAction(ctx, req.UserID, req.AppID,
		int64(x), int64(y), int64(deltaX), int64(deltaY), req.Xpath)
	if err != nil {
		return nil, err
	}

	return res, nil
}

// executeSearchAction 执行搜索操作
func executeSearchAction(
	ctx context.Context,
	req *sandbox_api_model.ActionReq,
) (*agent_service_model.PythonResult, error) {
	if req.Text == "" || (req.MarkConfig == nil && req.Xpath == "") || req.Screen == nil {
		return nil, fmt.Errorf("搜索操作需要提供text和markConfig和screen参数")
	}
	var x float64
	var y float64
	if req.Xpath == "" {
		// 计算实际点击坐标
		x = (req.MarkConfig.X + req.MarkConfig.Width/2) * float64(req.Screen.ScreenWidth)
		y = (req.MarkConfig.Y + req.MarkConfig.Height/2) * float64(req.Screen.ScreenHeight)
	}
	res, err := SearchAction(ctx, req.UserID, req.AppID, int64(x), int64(y), req.Xpath, req.Text)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// KillSandbox 关闭沙箱
func KillSandbox(ctx context.Context, req *sandbox_api_model.KillSandboxReq) (*base.Resp[any], error) {
	if err := checkTargetID(ctx, req.TargetID, req.UserID, req.AppID); err != nil {
		return nil, err
	}
	if req.UserID != "" && req.AppID != "" {
		_, err := TerminateAction(ctx, req.UserID, req.AppID)
		if err != nil {
			return nil, err
		}
	}
	resp := &base.Resp[any]{
		Message: "success",
	}
	return resp, nil
}

// SubmitAnnotation 提交标注内容
func SubmitAnnotation(ctx context.Context, req *sandbox_api_model.SubmitAnnotationReq) (*base.Resp[any], error) {
	// 创建一个新的标注记录
	annotation := sandbox_api_model.Annotation{
		RTX:         req.RTX,
		AppID:       req.AppID,
		TargetID:    req.TargetID,
		UserID:      req.UserID,
		Instruction: req.Instruction,
		SubmitTime:  req.SubmitTime,
		Operations:  req.Operations,
		Source:      req.Source,
	}

	err := db.CreateAnnotationWithOperations(&annotation)
	if err != nil {
		resp := &base.Resp[any]{
			Code:    400,
			Message: err.Error(),
		}
		return resp, nil
	}
	// 返回成功
	resp := &base.Resp[any]{
		Code:    0,
		Message: "success",
	}

	return resp, nil
}

// GetAnnotations 获取标注内容列表
func GetAnnotations(
	rtxValues []string, appID, targetID, instruction string,
	annotationIDs []int64, limit, offset int,
	isDeleted *int8, isEval *int8, source *int8, isReady *int8,
	startTime, endTime *time.Time,
) (*base.Resp[sandbox_api_model.GetAnnotationsRespData], error) {
	// 直接从数据库获取分页数据和总数
	total, pagedAnnotations, err := db.ListPagedAnnotationsWithOperations(
		rtxValues,
		appID,
		targetID,
		instruction,
		annotationIDs,
		limit,
		offset,
		isDeleted, isEval, source, isReady, startTime, endTime,
	)
	if err != nil {
		return nil, err
	}
	// 返回结果
	resp := &base.Resp[sandbox_api_model.GetAnnotationsRespData]{
		Code:    0,
		Message: "success",
		Data: &sandbox_api_model.GetAnnotationsRespData{
			Total:       total,
			Annotations: pagedAnnotations,
		},
	}
	return resp, nil
}

// DeleteAnnotation 软删除标注内容
func DeleteAnnotation(ctx context.Context, req *sandbox_api_model.DeleteAnnotationReq) (*base.Resp[any], error) {
	err := db.SoftDeleteAnnotation(req.AnnotationID)
	if err != nil {
		return &base.Resp[any]{
			Code:    400,
			Message: err.Error(),
		}, nil
	}
	// 返回成功
	return &base.Resp[any]{
		Code:    0,
		Message: "success",
	}, nil
}

// DeleteAnnotationOperation 软删除标注操作记录
func DeleteAnnotationOperation(
	ctx context.Context, req *sandbox_api_model.DeleteAnnotationOperationReq,
) (*base.Resp[any], error) {
	err := db.SoftDeleteAnnotationOperation(req.AnnotationID, req.OperationID)
	if err != nil {
		return &base.Resp[any]{
			Code:    400,
			Message: err.Error(),
		}, nil
	}
	// 返回成功
	return &base.Resp[any]{
		Code:    0,
		Message: "success",
	}, nil
}

// EvalAnnotation 将标注数据置为评估集
func EvalAnnotation(ctx context.Context, req *sandbox_api_model.EvalAnnotationReq) (*base.Resp[any], error) {
	// 调用数据库层
	err := db.EvalAnnotation(req.AnnotationID)
	if err != nil {
		return &base.Resp[any]{
			Code:    400,
			Message: err.Error(),
		}, nil
	}

	// 返回成功
	return &base.Resp[any]{
		Code:    0,
		Message: "success",
	}, nil
}

// UpdateAnnotationOperation 更新标注操作记录
func UpdateAnnotationOperation(
	ctx context.Context, req *sandbox_api_model.UpdateAnnotationOperationReq,
) (*base.Resp[any], error) {
	err := db.UpdateAnnotationOperation(req.AnnotationID, req.OperationID, req.Type,
		req.MarkConfig, req.Text, req.Length, req.Direction, req.Xpath)
	if err != nil {
		return &base.Resp[any]{
			Code:    400,
			Message: err.Error(),
		}, nil
	}
	// 返回成功
	return &base.Resp[any]{
		Code:    0,
		Message: "success",
	}, nil
}

// UpdateAnnotation 更新标注内容
func UpdateAnnotation(ctx context.Context, req *sandbox_api_model.UpdateAnnotationReq) (*base.Resp[any], error) {
	err := db.UpdateAnnotation(req.AnnotationID, req.Note)
	if err != nil {
		return &base.Resp[any]{
			Code:    400,
			Message: err.Error(),
		}, nil
	}
	// 返回成功
	return &base.Resp[any]{
		Code:    0,
		Message: "success",
	}, nil
}

// ReadyAnnotation 设置标注为 ready 状态
func ReadyAnnotation(ctx context.Context, req *sandbox_api_model.ReadyAnnotationReq) (*base.Resp[any], error) {
	// 调用数据库层
	err := db.ReadyAnnotation(req.AnnotationID)
	if err != nil {
		return &base.Resp[any]{
			Code:    400,
			Message: err.Error(),
		}, nil
	}

	// 返回成功
	return &base.Resp[any]{
		Code:    0,
		Message: "success",
	}, nil
}

func checkTargetID(ctx context.Context, targetID string, uin string, appID string) error {
	var err error
	if targetID == "" {
		targetID, err = db.GetLatestSessionTraceID(uin, appID)
		if err != nil {
			return errors.BadRequest.WithMessagef("获取traceID失败：error=%v", err)
		}
	}
	currentSpanTraceID := ""
	span := trace.SpanContextFromContext(ctx)
	if span.IsValid() {
		currentSpanTraceID = span.TraceID().String()
	}
	if targetID != currentSpanTraceID {
		return errors.BadRequest.WithMessagef(
			"traceID不匹配：trace_id=%s，current_span_trace_id=%s", targetID, currentSpanTraceID,
		)
	}
	return nil
}

func getCurrentPageInfo(
	ctx context.Context, pythonResult *agent_service_model.PythonResult,
) (*sandbox_api_model.ActionRespData, error) {
	// 检查 pythonResult 是否为 nil
	if pythonResult == nil {
		return nil, fmt.Errorf("pythonResult is nil")
	}

	// 检查 StandardOutputList 是否为空
	if len(pythonResult.StandardOutputList) == 0 {
		return nil, fmt.Errorf("StandardOutputList is empty")
	}

	lastOutput := pythonResult.StandardOutputList[len(pythonResult.StandardOutputList)-1]

	// 检查 ResizedSize 是否有足够的元素
	if len(lastOutput.ScreenshotDTO.ResizedSize) < 2 {
		return nil, fmt.Errorf("ResizedSize does not have enough elements")
	}

	imgURL := lastOutput.ScreenshotDTO.URL
	imgWidth := lastOutput.ScreenshotDTO.ResizedSize[0]
	imgHeight := lastOutput.ScreenshotDTO.ResizedSize[1]

	domXML := ""
	allElementsRectsStr := ""
	extraInfo := make(map[string]interface{})
	var err error
	if len(pythonResult.StandardOutputList) > 0 {
		lastStandardOutput := pythonResult.StandardOutputList[len(pythonResult.StandardOutputList)-1]
		domXML, err = getDomXML(lastStandardOutput.DomXML["data"])
		if err != nil {
			log.WithContext(ctx, log.Field{Key: "error", Value: err}).Error("获取DOM XML失败")
		}
		allNonNativeElementsRects, err := getElementsRects(lastStandardOutput.ElementsRects["data"])
		if err != nil {
			log.WithContext(ctx, log.Field{Key: "error", Value: err}).Error("获取所有非原生元素矩形失败")
		}
		allNativeElementsRects, err := getElementsRects(lastStandardOutput.NativeElementsRects["data"])
		if err != nil {
			log.WithContext(ctx, log.Field{Key: "error", Value: err}).Error("获取原生元素矩形失败")
		}

		// 合并所有元素矩形
		allElementsRects := append([]interface{}{}, allNonNativeElementsRects...)
		allElementsRects = append(allElementsRects, allNativeElementsRects...)
		allElementsRectsBytes, err := json.Marshal(allElementsRects)
		if err != nil {
			log.WithContext(ctx, log.Field{Key: "error", Value: err}).Error("序列化所有元素矩形失败")
		} else {
			allElementsRectsStr = string(allElementsRectsBytes)
		}
		// 获取页面URL
		pageURL, err := getPageURL(lastStandardOutput.AppletPageInfo["data"])
		if err != nil {
			log.WithContext(ctx, log.Field{Key: "error", Value: err}).Error("获取页面URL失败")
		} else {
			log.WithContext(ctx, log.Field{Key: "pageURL", Value: pageURL}).Info("获取页面URL成功")
			extraInfo["pageURL"] = pageURL
		}
	}

	// 返回结果
	return &sandbox_api_model.ActionRespData{
		URL:              imgURL,
		DomXML:           domXML,
		AllElementsRects: allElementsRectsStr,
		ExtraInfo:        extraInfo,
		ScreenWidth:      imgWidth,  // 使用实际图片宽度
		ScreenHeight:     imgHeight, // 使用实际图片高度
	}, nil
}

func getDomXML(domXML json.RawMessage) (string, error) {
	dataMap := make(map[string]interface{})
	if err := json.Unmarshal(domXML, &dataMap); err == nil {
		if resultData, ok := dataMap["result"].(map[string]interface{}); ok {
			if innerResult, ok := resultData["result"].(map[string]interface{}); ok {
				if value, ok := innerResult["value"].(string); ok {
					return value, nil
				}
			}
		}
	}
	return "", fmt.Errorf("DOM XML结果格式错误")
}

func getElementsRects(elementsRects json.RawMessage) ([]interface{}, error) {
	dataMap := make(map[string]interface{})
	if err := json.Unmarshal(elementsRects, &dataMap); err == nil {
		if resultData, ok := dataMap["result"].(map[string]interface{}); ok {
			if innerResult, ok := resultData["result"].(map[string]interface{}); ok {
				if value, ok := innerResult["value"].([]interface{}); ok {
					return value, nil
				}
			}
		}
	}
	return nil, fmt.Errorf("所有元素矩形结果格式错误")
}

func getPageURL(appletPageInfo json.RawMessage) (string, error) {
	dataMap := make(map[string]interface{})
	if err := json.Unmarshal(appletPageInfo, &dataMap); err == nil {
		if resultData, ok := dataMap["data"].(map[string]interface{}); ok {
			if pageURL, ok := resultData["path"].(string); ok {
				return pageURL, nil
			}
		}
	}
	return "", fmt.Errorf("页面URL结果格式错误")
}
