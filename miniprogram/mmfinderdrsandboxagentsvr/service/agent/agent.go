// Package agent 执行 agent 推理算法
package agent

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"time"

	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/wego/wxg/weenv"
	"git.woa.com/wxg-mmfinderluban/mmfinderluban/miniprogram/mmfinderdrsandboxbase/errors"
	"git.woa.com/wxg-mmfinderluban/mmfinderluban/miniprogram/mmfinderdrsandboxbase/model/api/base"

	agent_api_model "mmfinderdrsandboxagentsvr/model/api/agent"
	agent_service_model "mmfinderdrsandboxagentsvr/model/service/agent"
	"mmfinderdrsandboxagentsvr/service/sandbox"
)

var localhostClient thttp.Client

// Init 初始化
func Init() {
	localhostClient = thttp.NewClientProxy("trpc.mmfinderdrsandbox.mmfinderdrsandboxagentsvr.python_http")
}

// Run 执行 agent 推理算法
func Run(
	ctx context.Context,
	authCode string,
	position *agent_api_model.PositionInfo,
	skipShareURLData bool,
	args map[string]string,
) (resp *base.Resp[agent_api_model.BaseRespData], err error) {
	resp = &base.Resp[agent_api_model.BaseRespData]{
		Data: &agent_api_model.BaseRespData{},
	}
	// 1. 执行 Python 脚本并解析结果
	pyResultDTO, err := RunAgentInferPython(ctx, authCode, position, skipShareURLData, args)
	if err != nil {
		resp.SetErr(ctx, 1, err)
		return
	}
	thinkingBytes, err := json.Marshal(pyResultDTO.PythonResult)
	if err != nil {
		resp.SetErr(ctx, 2, err)
	}
	resp.Data.ShareURL = fmt.Sprintf("http://%s:%d+%s+%s+%s", weenv.GetInnerIp(), 80, "0", "fixrequestid", "thinkingStr")
	resp.Data.Thinking = []string{string(thinkingBytes)}
	screenshotURLList := []string{}
	for _, standardOutput := range pyResultDTO.PythonResult.StandardOutputList {
		screenshotURLList = append(screenshotURLList, standardOutput.ScreenshotDTO.URL)
	}
	resp.Data.LongImgURL = pyResultDTO.PythonResult.LongImgURL
	resp.Data.LongUniqueImgURL = pyResultDTO.PythonResult.LongUniqueImgURL
	resp.Data.ScreenshotImgURLs = screenshotURLList
	// 2. 拿到的分享数据需要进行二次转换做适配
	updatedData, err := processSharedData(ctx, pyResultDTO.SharedData)
	if err != nil {
		resp.SetErr(ctx, 5, err)
		return
	}
	resp.Data.DebugStr = updatedData
	return
}

// RunAgentInferPython 调用 python 本地推理服务
func RunAgentInferPython(
	ctx context.Context,
	authCode string,
	position *agent_api_model.PositionInfo,
	skipShareURLData bool,
	args map[string]string,
) (*agent_service_model.PythonResultDTO, error) {
	res := &agent_service_model.PythonResultDTO{
		PythonResult: &agent_service_model.PythonResult{},
	}
	if authCode != "" && position != nil {
		// 1. 初始化沙箱环境
		_, _ = sandbox.SandboxOnlineObj.Logout(ctx)
		if _, err := sandbox.SandboxOnlineObj.Login(ctx, authCode); err != nil {
			return res, errors.ReqFailed.WithError(err)
		}
		defer func() {
			_, _ = sandbox.SandboxOnlineObj.Logout(ctx)
		}()
		if _, err := sandbox.SandboxOnlineObj.SetLocation(ctx, position.Latitude, position.Longitude); err != nil {
			return res, errors.ReqFailed.WithError(err)
		}
	}
	// 2. 调用本地推理服务
	startTime := time.Now()
	resp := &base.Resp[agent_service_model.PythonResult]{
		Data: &agent_service_model.PythonResult{},
	}
	if err := localhostClient.Post(ctx, "/v1/task", &args, &resp); err != nil {
		return res, errors.ReqFailed.WithError(fmt.Errorf("调用推理服务失败: %v", err))
	}
	runTime := time.Since(startTime).String()
	// 3. 处理结果
	// 3.1 错误处理
	if resp.Code != 0 {
		errMsg := "调用推理服务失败"
		log.WithContext(
			ctx,
			log.Field{Key: "run_time", Value: runTime},
			log.Field{Key: "resp_code", Value: resp.Code},
		).Error(errMsg)
		return res, errors.BadRetCode.WithMessagef(
			"%s：run_time=%s，resp_code=%v", errMsg, runTime, resp.Code,
		)
	}
	// 3.2 正常处理
	res.PythonResult = resp.Data
	// 获取分享链接
	if !skipShareURLData {
		data, err := sandbox.SandboxOnlineObj.GetShareURLData(ctx, res.PythonResult.TargetID)
		if err != nil {
			errMsg := "获取小程序分享链接失败"
			log.WithContext(
				ctx,
				log.Field{Key: "error", Value: err},
			).Error(errMsg)
			return res, errors.ReqFailed.WithMessagef("%s ,error=%v", errMsg, err)
		}
		res.SharedData = data
	}
	return res, nil
}

func processSharedData(ctx context.Context, data string) (res string, err error) {
	if data == "" {
		return
	}
	// 1. 将 JSON 字符串解析为 map
	var dataMap map[string]interface{}
	if err = json.Unmarshal([]byte(data), &dataMap); err != nil {
		return
	}
	// 2. 提取 thumbIconPath 的值
	thumbIconPath, ok := dataMap["thumbIconPath"].(string)
	if !ok {
		err = fmt.Errorf("thumbIconPath not found or is not a string")
		return
	}
	// 3. 读取图片文件
	imageData, err := os.ReadFile(thumbIconPath)
	if err != nil {
		log.WithContext(
			ctx,
			log.Field{Key: "thumbIconPath", Value: thumbIconPath},
			log.Field{Key: "error", Value: err},
		).Warn("读取图片文件失败，将 thumbIconPath 置空")
		dataMap["thumbIconPath"] = "" // 置空字段
	} else {
		// 对图片内容进行 Base64 编码
		encoded := base64.StdEncoding.EncodeToString(imageData)
		dataMap["thumbIconPath"] = encoded // 更新字段
	}
	// 4. 将 map 重新序列化为 JSON 字符串
	updatedData, err := json.Marshal(dataMap)
	if err != nil {
		return
	}
	res = string(updatedData)
	return
}
