#!/usr/bin/sh

# you can add your shell here

# use supervisor to start module sbin
nohup env https_proxy=mmminiprogramhttpproxy.production.polaris:11113 \
    http_proxy=mmminiprogramhttpproxy.production.polaris:11113 \
    HTTPS_PROXY=mmminiprogramhttpproxy.production.polaris:11113 \
    HTTP_PROXY=mmminiprogramhttpproxy.production.polaris:11113 \
    NO_PROXY="localhost,127.0.0.1/8,::1" \
    no_proxy="localhost,127.0.0.1/8,::1" \
    bash /home/<USER>/workspace/start.sh > /home/<USER>/workspace/start.log 2>&1 &

mkdir -p /home/<USER>/data/mmfinderdrsandboxagentsvr
nohup /usr/bin/supervisord -c /home/<USER>/mmfinderdrsandboxagentsvr/etc/supervisor_test.conf >/home/<USER>/data/mmfinderdrsandboxagentsvr/supervisor_test.log 2>&1 &
sleep infinity
