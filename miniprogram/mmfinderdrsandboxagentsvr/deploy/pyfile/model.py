"""
定义脚本的输入输出类型
"""

import json
import traceback
from enum import StrEnum
from typing import Any

from opentelemetry import trace
from pydantic import BaseModel, Field
from wxms.logger import logger
from wxms.model.service.sandbox_service import (
    SandboxActionStandardOutput,
    TaskNamespace,
)
from wxms.service.clickhouse_service import Step, clickhouse_service
from wxms.service.sandbox_service import SandboxClient
from wxms.util.common_util import CustomJSONEncoder
from wxms.util.context_util import context_util
from wxms.util.time_util import time_util

DEFAULT_PROMPT = """你是一个小程序操作助手，现在需要操作小程序来完成用户的需求，整个过程可能需要进行多步的操作和尝试才能完成
请尽可能的进行尝试和探索来完成用户的需求

## 输入信息
- 用户输入：任务指令或者补充信息，将会包含在<user_input></user_input>标签中
- 小程序状态：根据小程序页面的dom树进行解析和简化后的结构化文本，将会包含在<state_i></state_i>标签中（代表当前操作第i步）
  - 每个可操作的元素开头都会有一个唯一的id标识在[]内（比如`[n]`代表当前元素的id是n）
  - 会用缩进代表页面上的一些层级关系
  - clickable代表元素可进行点击；typeable代表元素可进行输入；scrollable代表元素可进行滑动；有时这些事件也会继承给层级内部的子元素
  - 元素的描述信息有时会使用英文或者中英混杂，这属于正常情况

## 任务
1. 你需要以完成用户的需求为主要目标，对当前小程序的状态进行分析，并从tools中选择合适的tool对小程序进行操作
2. 优先使用搜索能力来查找需要的内容，有的搜索框不会标注typeable，只会有clickable，你需要根据文字描述进行合理的推断
3. 每次操作完成后，你将会收到最新的小程序状态信息
4. 整个操作过程需要反复迭代和尝试，直到任务完成
5. 如果当前时间不在小程序的工作范围内，及时终止并告知用户相关信息

## 注意事项
- 购物车内有其他无关商品时，尝试进行剔除
- 购物、点单、消费任务进入到支付页面即可terminate，无需点击支付操作
- 你只对最新的state中展示的元素编号进行操作
- 如果当页面元素中出现有关：*服务使用条款*、*隐私协议*等信息时，**点击上层/前面的同意按钮**（注意不是条款内容），否则其他的操作（登录、支付等）可能无法进行点击
- 如果发现无法完成任务，转变思路再尝试一下
- 页面状态为空时，wait一段时间后再操作
- 不要擅自篡改或简化用户的需求
- 需要切换位置时，如果没有明确的可操作元素，可尝试点击地点来切换位置
- 如果可以进行搜索，优先考虑使用搜索功能

**请将当前任务状态的总结以及下一步的规划输出在<think></think>标签内，字数小于150字**"""
DEFAULT_PROMPT_VLT = """
你是一个小程序操作助手，上述图像是按顺序操作任务指令，最近一张是当前图像。
当前指令: 如果当前页面能够搜索优先使用搜索功能来实现指令，{}，请问在当前图像里我要怎么操作？当前只能有一种输出选项：只能回复接下来的一个动作，不能回复多个动作，并给出在页面的精确像素坐标（x y），最终输出格式严格遵循<think>xxx</think><answer>xxx</answer>这个格式。answer动作只能在以下6 类集合选择：1. click x y(代表点击像素坐标 x,y)，2. input x y t（代表在x y 选中输入框并输入文本t） 3. finish（代表任务成功结束） 4. stop（代表任务无法进行 进程终止）5. scroll x（代表下滑x个像素，x为负数代表上滑|x|个像素）6. wait t（代表当前页面处于加载中，可以等待t秒）。 思考内容：首先通过对话上下文判断上一步的操作是否生效，如果上一步操作无效，需要反思并采取正确的动作，例如 1.  input x y t 后页面是否有显示 input 的内容t，没有的话则操作未生效，考虑网页不支持本步input 而应该采取click x y 操作使用点击小程序拉起的键盘输入 2. click x y 后操作未生效考虑点击位置无效，应该采取点击其他位置click x1 y1实现指令 3. assistant重复输入相同命令表示当前操作无效，需要采取其他方法实现指令 4. 需要详细检查 input 的内容 t 是否正确显示在页面中 5. 最近两张图像没有变化时需要反思动作是否正确
"""
DEFAULT_PROMPT_VLT_V1 = """你是一个小程序操作助手，上述图像是按顺序操作任务指令，最近一张是当前图像。
当前指令: {}，用户历史操作总结为:{}，请问在当前图像里我要怎么操作？当前只能有一种输出选项：只能回复接下来的一个动作，不能回复多个动作，并给出在页面的精确像素坐标（x y），最终输出格式严格遵循<think>xxx</think><answer>xxx</answer><summary>xxx</summary>这个格式。

思考过程放在<think>、</think>标签内。思考内容：首先通过对话上下文判断上一步的操作是否生效，如果上一步操作无效，需要反思并采取正确的动作，例如 1.  input x y t 后页面是否有显示 input 的内容t，没有的话则操作未生效，考虑网页不支持本步input 而应该采取click x y 操作使用点击小程序拉起的键盘输入 2. click x y 后操作未生效考虑点击位置无效，应该采取点击其他位置click x1 y1实现指令 3. assistant重复输入相同命令表示当前操作无效，需要采取其他方法实现指令 4. 需要详细检查 input 的内容 t 是否正确显示在页面中 5. 最近两张图像没有变化时需要反思动作是否正确。

操作动作放在<answer>、</answer>标签内。操作动作只能在以下6 类集合选择：1. click x y(代表点击像素坐标 x,y)，2. input x y t（代表在x y 选中输入框并输入文本t） 3. finish（代表任务成功结束） 4. stop（代表任务无法进行 进程终止）5. scroll x（代表下滑x个像素，x为负数代表上滑|x|个像素）6. wait t（代表当前页面处于加载中，可以等待t秒）。

单步操作总结放在<summary>、</summary>标签内，主要对本次的操作动作进行总结，单步总结操作示例为："点击同意按钮,同意用户协议"、"点击确认按钮,选择当前门店并进入支付页面"、"点击韩元按钮"等。注意：如果是click操作，总结为"点击xxx按钮,yyy"，其中xxx为按钮的名字，yyy为用户操作意图；如果是input操作，总结为点击xxx并输入yyy，其中xxx为输入框名字，yyy为输入内容；如果是scroll操作，总结为"上滑/下滑x个像素"，其中x为像素个数；如果是wait操作，总结为"等待t秒"，其中t为等待的秒数。

注意：1.如果当前页面支持搜索功能，优先使用搜索来实现任务目标。2.如果当前页面是支付二维码页面，则可以考虑任务完成，结束流程。3.如果当前页面处于确认付款阶段，请检查购物车中的商品是否符合任务要求，若不符合要求，请先删除不需要的商品，再继续下一步操作。"""  # pylint: disable=C0301


class PythonInput(BaseModel):
    """
    脚本的输入类型
    """

    app_entry_url: str = Field("")
    app_id: str = Field("wx3dcca19d0aa51755")
    app_name: str = Field("星巴克")
    base_url: str = Field("http://drhttpsvr.polaris:8000/v1/llm_luban_user_xiaodezhang_llm_luban_xiaochengxu_qwen25vl32b_v20250704_360_v16_export-0707-19/")  # fmt: skip # pylint: disable=C0301
    from_username: str = Field("")
    headless_mode: str = Field("0")
    instruction: str = Field("帮我点一杯冰美式咖啡")
    model_name: str = Field("llm_luban_user_xiaodezhang_llm_luban_xiaochengxu_qwen25vl32b_v20250704_360_v16_export-0707-19")  # fmt: skip # pylint: disable=C0301
    namespace: TaskNamespace = Field(TaskNamespace.UNKNOWN)
    prompt: str = Field(DEFAULT_PROMPT)
    prompt_vlt: str = Field(DEFAULT_PROMPT_VLT)
    prompt_vlt_v1: str = Field(DEFAULT_PROMPT_VLT_V1)
    rag_base_url: str = Field("http://mmfinderdrannotationsvr.polaris:8080")
    rag_extra_config: str = Field("")
    raw_query: str = Field("帮我点一杯冰美式咖啡")
    run_mode: str = Field("text_infer_v3")
    sandbox_check_screenshot_loading_status_max_retry_count: str = Field("3")
    sandbox_check_screenshot_loading_status_max_retry_count_for_back: str = Field("0")
    sandbox_close_applet: str = Field("1")
    sandbox_close_applet_with_session_alive: str = Field("0")
    sandbox_is_async_action_result: str = Field("0")
    sandbox_link_type: str = Field("")
    sandbox_skip_launch_applet: str = Field("0")
    sandbox_sleep_seconds_before_screenshot: str = Field("0.2")
    special_app_id_list: str = Field("")
    special_str_1: str = Field("")
    special_str_2: str = Field("")
    uin: str = Field("0")
    username: str = Field("")
    vlt_base_url: str = Field("http://drhttpsvr.polaris:8000/v1/llm_luban_xiaochengxu_qwen25vl32b_v20250605_02_checkpoint-650_export-0606-15/")  # fmt: skip # pylint: disable=C0301
    vlt_base_url_v1: str = Field("http://drhttpsvr.polaris:8000/v1/llm-luban-702_didi_Qwen2.5-VL-32B-didi702dest1959_ck140_export-0703-15")  # fmt: skip # pylint: disable=C0301
    vlt_model_name: str = Field("llm_luban_xiaochengxu_qwen25vl32b_v20250605_02_checkpoint-650_export-0606-15")  # fmt: skip # pylint: disable=C0301
    vlt_model_name_v1: str = Field("llm-luban-702_didi_Qwen2.5-VL-32B-didi702dest1959_ck140_export-0703-15")  # fmt: skip # pylint: disable=C0301


class PythonResultStatus(StrEnum):
    """
    脚本输出的参数 status 枚举
    """

    FAILED = "failed"
    SUCCESS = "success"


class PythonResult(BaseModel):
    """
    脚本的输出类型
    """

    answers: list[str] = Field(..., title="各个 agent 自定义的返回")
    answers_raw_data: list[str] = Field(..., title="inference 阶段大模型输出的 raw data，数量应该是推理步骤的整数倍")  # fmt: skip # pylint: disable=C0301
    interrupt: dict = Field(..., title="中断信息，为空则不是中断退出")
    log_data: list[dict[str, Any]] = Field(..., title="debug 的 log 数据")
    long_img_url: str = Field(..., title="所有图片纵向拼接后的 url")
    long_unique_img_url: str = Field(..., title="图片 hash 去重后纵向拼接后的 url")
    screens: list[str] = Field(..., title="文本模型才添加：dom xml 的简化，作为大模型输入的一部分能更好地反映关键信息，和 standard_output_list 一一对应")  # fmt: skip # pylint: disable=C0301
    standard_output_list: list[SandboxActionStandardOutput] = Field(..., title="标准操作的输出列表")  # fmt: skip # pylint: disable=C0301
    status: PythonResultStatus = Field(..., title="脚本执行的状态")
    target_id: str = Field(..., title="小程序页面的 target_id")


class SandboxAgentBaseMeta(type):
    """
    沙箱 Agent 推理类的元类，自动装饰一些方法
    """

    def __new__(mcs, name, bases, namespace):
        if "init_step" in namespace:
            namespace["init_step"] = context_util.add_trace_span()(
                namespace["init_step"]
            )
        if "run_one_step" in namespace:
            namespace["run_one_step"] = context_util.add_trace_span()(
                namespace["run_one_step"]
            )
        return super().__new__(mcs, name, bases, namespace)


class SandboxAgentBase(metaclass=SandboxAgentBaseMeta):
    """
    沙箱 Agent 推理类
    """

    def __init__(
        self,
        python_input: PythonInput,
        python_result: PythonResult,
        sandbox_client: SandboxClient,
    ):
        self.python_input = python_input
        self.python_result = python_result
        self.sandbox_client = sandbox_client
        self.max_step = 50
        self.step_count = -1
        self.last_trace_answer_index = -1
        self.last_trace_answer_raw_data_index = -1
        self.last_trace_log_data_index = -1
        self.last_trace_screen_index = -1

    def init_step(self):
        """
        初始化步骤，大部分代码其实都应该放在 __init__ 中
        但如果为了 instrumentation，可以将一些 trace 上报放在这里
        """
        started_at = time_util.get_millisecond_timestamp_of_current_time()
        context_util.set_data_into_context(
            "sandbox_step_id", context_util.get_span_id()
        )
        error_dict = {}
        try:
            self.init_step_internal()
        except Exception as e:
            error_dict = {
                "exception": str(e),
                "traceback": traceback.format_exc(),
            }
            logger.error(
                "init_step failed",
                extra={
                    "customized_data_info": error_dict,
                },
            )
            raise e
        finally:
            # 给所在 span 添加信息
            clickhouse_service.add_step(
                Step(
                    started_at=started_at,
                    ended_at=time_util.get_millisecond_timestamp_of_current_time(),
                    session_id=context_util.get_trace_id(),
                    sub_session_id=context_util.get_data_from_context(
                        "sandbox_sub_session_id"
                    ),
                    step_id=context_util.get_data_from_context("sandbox_step_id"),
                    answer=json.dumps(
                        self.trace_add_answer_event(), cls=CustomJSONEncoder
                    ),
                    answer_raw_data=json.dumps(
                        self.trace_add_answer_raw_data_event(), cls=CustomJSONEncoder
                    ),
                    log_data=json.dumps(
                        self.trace_add_log_data_event(), cls=CustomJSONEncoder
                    ),
                    screen=json.dumps(
                        self.trace_add_screen_event(), cls=CustomJSONEncoder
                    ),
                    result=json.dumps(error_dict, cls=CustomJSONEncoder),
                    step_type=self.init_step.__name__,
                )
            )
            context_util.set_data_into_context("sandbox_step_id", "")

    def init_step_internal(self):
        """
        初始化步骤，让子类重写的方法
        """

    def run(self):
        """
        沙箱推理
        """
        for step_count in range(self.max_step):
            self.step_count = step_count
            if self.run_one_step():
                break

    def run_one_step(self) -> bool:
        """
        沙箱单步推理

        :return: 是否推理结束
        """
        started_at = time_util.get_millisecond_timestamp_of_current_time()
        context_util.set_data_into_context(
            "sandbox_step_id", context_util.get_span_id()
        )
        error_dict = {}
        try:
            res = self.run_one_step_internal()
        except Exception as e:
            error_dict = {
                "exception": str(e),
                "traceback": traceback.format_exc(),
            }
            logger.error(
                "run_one_step failed",
                extra={
                    "customized_data_info": error_dict,
                },
            )
            raise e
        finally:
            # 给所在 span 添加信息
            clickhouse_service.add_step(
                Step(
                    started_at=started_at,
                    ended_at=time_util.get_millisecond_timestamp_of_current_time(),
                    session_id=context_util.get_trace_id(),
                    sub_session_id=context_util.get_data_from_context(
                        "sandbox_sub_session_id"
                    ),
                    step_id=context_util.get_data_from_context("sandbox_step_id"),
                    answer=json.dumps(
                        self.trace_add_answer_event(), cls=CustomJSONEncoder
                    ),
                    answer_raw_data=json.dumps(
                        self.trace_add_answer_raw_data_event(), cls=CustomJSONEncoder
                    ),
                    log_data=json.dumps(
                        self.trace_add_log_data_event(), cls=CustomJSONEncoder
                    ),
                    screen=json.dumps(
                        self.trace_add_screen_event(), cls=CustomJSONEncoder
                    ),
                    result=json.dumps(error_dict, cls=CustomJSONEncoder),
                    step_type=self.run_one_step.__name__,
                )
            )
            context_util.set_data_into_context("sandbox_step_id", "")
        return res

    def run_one_step_internal(self) -> bool:
        """
        沙箱单步推理，让子类重写的方法

        :return: 是否推理结束
        """
        return False

    def trace_add_answer_event(self) -> list[str]:
        """
        给所在 span 添加 answer 信息
        这个信息的产生由各自业务决定
        但强烈建议这个信息的产生在 observation 之后，且一个 run_one_step 中只有一个 answer
        """
        data = self.python_result.answers[self.last_trace_answer_index + 1 :]
        self.last_trace_answer_index = len(self.python_result.answers) - 1
        current_span = trace.get_current_span()
        for i in data:
            current_span.add_event(
                name="answer",
                attributes={
                    "name": "answer",
                    "data": i,
                },
            )
        return data

    def trace_add_answer_raw_data_event(self) -> list[str]:
        """
        给所在 span 添加 answer_raw_data 信息（inference 阶段大模型输出的 raw data）
        这个信息的产生介于 inference 和 action 之间
        由于 run_one_step 的一次执行中可能有多次模型调用，所以这里 data 是一个 list
        """
        data = self.python_result.answers_raw_data[
            self.last_trace_answer_raw_data_index + 1 :
        ]
        self.last_trace_answer_raw_data_index = (
            len(self.python_result.answers_raw_data) - 1
        )
        current_span = trace.get_current_span()
        for i in data:
            current_span.add_event(
                name="answer_raw_data",
                attributes={
                    "name": "answer_raw_data",
                    "data": i,
                },
            )
        return data

    def trace_add_log_data_event(self) -> list[dict[str, Any]]:
        """
        给所在 span 添加 log_data 信息
        这个信息的产生介于 inference 和 action 之间（为了方便定位问题）
        由于 run_one_step 的一次执行中可能会执行多个 action，所以这里 data 是一个 list
        """
        data = self.python_result.log_data[self.last_trace_log_data_index + 1 :]
        self.last_trace_log_data_index = len(self.python_result.log_data) - 1
        current_span = trace.get_current_span()
        for i in data:
            current_span.add_event(
                name="log_data",
                attributes={
                    "name": "log_data",
                    "data": json.dumps(i, cls=CustomJSONEncoder),
                },
            )
        return data

    def trace_add_screen_event(self) -> list[str]:
        """
        给所在 span 添加 screen_list 信息，表示 dom xml 的简化，作为大模型输入的一部分能更好地反映关键信息
        这个信息的产生在 observation 之后
        由于 run_one_step 的一次执行中可能会执行多个 action，所以这里 data 是一个 list
        """
        data = self.python_result.screens[self.last_trace_screen_index + 1 :]
        self.last_trace_screen_index = len(self.python_result.screens) - 1
        current_span = trace.get_current_span()
        for i in data:
            current_span.add_event(
                name="screen",
                attributes={"name": "screen", "data": i},
            )
        return data
