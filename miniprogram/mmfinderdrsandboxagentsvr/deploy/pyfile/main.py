"""
主入口
"""

import argparse
import sys

from model import PythonInput
from task_handler import execute_task
from wxms.logger import close_logger
from wxms.service.clickhouse_service import clickhouse_service


def __get_python_input() -> PythonInput:
    parser = argparse.ArgumentParser(description="")
    for key, _ in PythonInput.model_fields.items():
        parser.add_argument(f"--{key}", default="")
    args = parser.parse_args()
    res = PythonInput(**{k: v for k, v in args.__dict__.items() if v})
    return res


def __main():
    python_input = __get_python_input()
    python_result = execute_task(python_input)
    # 将输出通过 stdout 返回
    sys.stdout.write("\n" + python_result.model_dump_json() + "\n")
    sys.stdout.flush()
    clickhouse_service.flush()
    close_logger()
    # 确保后续不会有其他打印输出干扰
    sys.exit(0)


if __name__ == "__main__":
    __main()
