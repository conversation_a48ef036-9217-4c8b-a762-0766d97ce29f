"""
redis 数据访问类
"""

from enum import IntEnum

from wxms.config.config import MIDDLEWARE_CONFIG
from wxms.middleware.redis_pool import RedisPool
from wxms.util.context_util import context_util


class RedisDBNum(IntEnum):
    """
    redis 的数据库编号
    """

    DEFAULT = 0
    APPLET_XPATH_PROMPT = 1
    IMAGE_OBJECT_KEY_DIR = 2
    PAGE_LOADED_IMAGE_HASH_CACHE = 3  # 加载完成页面图像哈希缓存


class RedisDAO:
    """
    redis 数据访问类
    """

    def __init__(self):
        self.redis_pool = RedisPool(
            host=MIDDLEWARE_CONFIG.redis_config.host,
            port=MIDDLEWARE_CONFIG.redis_config.port,
            password=MIDDLEWARE_CONFIG.redis_config.password,
            init_dbs=list(set([i.value for i in RedisDBNum])),
        )

    @context_util.add_trace_span(record_input=True, record_output=True)
    def image_object_key_dir_get(
        self,
        app_id: str,
        uin: int,
        request_id: str,
    ) -> str:
        """
        获取存储图片在对象存储上的 object key 文件夹，用于构成 object_key

        :param app_id: 小程序的 id
        :param uin: 微信 uin
        :param request_id: SandboxClient 对象的唯一标识，通常是 trace id
        :return: 存储图片在对象存储上的 object key 文件夹
        """
        client = self.redis_pool.get_redis(RedisDBNum.IMAGE_OBJECT_KEY_DIR.value)
        cache_key = f"image_object_key_dir:{app_id}:{uin}:{request_id}"
        temp_res = client.get(cache_key)
        return str(temp_res, encoding="utf-8") if temp_res is not None else ""  # type: ignore

    @context_util.add_trace_span(record_input=True, record_output=True)
    def image_object_key_dir_set(
        self, app_id: str, uin: int, request_id: str, value: str
    ) -> bool:
        """
        设置存储图片在对象存储上的 object key 文件夹，缓存时间设置2天，所以不能有一个操作

        :param app_id: 小程序的 id
        :param uin: 微信 uin
        :param request_id: SandboxClient 对象的唯一标识，通常是 trace id
        :param value: 存储图片在对象存储上的 object key 文件夹
        :return: 是否设置成功
        """
        client = self.redis_pool.get_redis(RedisDBNum.IMAGE_OBJECT_KEY_DIR.value)
        cache_key = f"image_object_key_dir:{app_id}:{uin}:{request_id}"
        return bool(client.set(cache_key, value, ex=60 * 60 * 24 * 2, nx=True))

    @context_util.add_trace_span(record_input=True, record_output=True)
    def page_loaded_image_hash_cache_get(
        self, app_id: str, version: str, full_path: str, image_hash: str
    ) -> bool:
        """
        查询页面截图是否命中 hash 缓存，缓存命中则表示截图是页面加载完成时的图片

        :param app_id: 小程序的 id
        :param version: 小程序的版本号
        :param full_path: 页面完整路径
        :param image_hash: 被检查截图的 hash 值
        :return: 是否已缓存
        """
        client = self.redis_pool.get_redis(
            RedisDBNum.PAGE_LOADED_IMAGE_HASH_CACHE.value
        )
        cache_key = f"page_loaded_image_hash_cache:{app_id}:{version}:{full_path}"
        return bool(client.sismember(cache_key, image_hash))

    @context_util.add_trace_span(record_input=True, record_output=True)
    def page_loaded_image_hash_cache_set(
        self,
        app_id: str,
        version: str,
        full_path: str,
        image_hash: str,
    ) -> bool:
        """
        设置页面加载完成时的图片 hash 缓存，缓存时间为 1 天

        :param app_id: 小程序的 id
        :param version: 小程序的版本号
        :param full_path: 页面完整路径
        :param image_hash: 页面加载完成时的图片 hash 值
        :return: 是否设置成功
        """
        client = self.redis_pool.get_redis(
            RedisDBNum.PAGE_LOADED_IMAGE_HASH_CACHE.value
        )
        cache_key = f"page_loaded_image_hash_cache:{app_id}:{version}:{full_path}"
        added = client.sadd(cache_key, image_hash)
        if added and client.ttl(cache_key) == -1:
            client.expire(cache_key, 60 * 60 * 24)
        return bool(added)


redis_dao = RedisDAO()

if __name__ == "__main__":
    from wxms.logger import logger

    temp_value = redis_dao.image_object_key_dir_get(
        app_id="app_id", uin=0, request_id="request_id"
    )
    logger.info(temp_value)
    logger.info(type(temp_value))
    logger.info(
        redis_dao.image_object_key_dir_set(
            app_id="app_id", uin=0, request_id="request_id", value="1"
        )
    )
    temp_value = redis_dao.image_object_key_dir_get(
        app_id="app_id", uin=0, request_id="request_id"
    )
    logger.info(temp_value)
    logger.info(type(temp_value))
