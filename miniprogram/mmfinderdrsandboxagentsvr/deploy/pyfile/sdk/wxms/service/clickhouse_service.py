"""
clickhouse 工具类
"""

import csv
import io
import json
import logging
import traceback
from enum import StrEnum
from functools import wraps
from typing import Any

import pulsar
from pydantic import BaseModel
from wxms.config.config import MIDDLEWARE_CONFIG
from wxms.logger import logger
from wxms.model.service.sandbox_service import TaskNamespace
from wxms.util.common_util import CustomJSONEncoder
from wxms.util.context_util import TracedThreadPoolExecutor, context_util
from wxms.util.time_util import time_util


class Action(BaseModel):
    """
    action 表的一行记录，唯一键 session_id + sub_session_id + step_id + action_id
    注意字段顺序要和 fisher 上注册的 pulsar 严格一致
    """

    started_at: int
    ended_at: int
    session_id: str
    sub_session_id: str
    step_id: str
    action_id: str
    parent_action_id: str
    action_type: str
    parameter: str
    result: str


class Event(BaseModel):
    """
    event 表的一行记录，该表不做唯一键约束
    注意字段顺序要和 fisher 上注册的 pulsar 严格一致
    """

    started_at: int
    session_id: str
    sub_session_id: str
    step_id: str
    action_id: str
    event_type: str
    parameter: str


class EventType(StrEnum):
    """
    event 类型枚举
    """

    CHECK_SCREENSHOT_LOADING_STATUS_EVENT = "CHECK_SCREENSHOT_LOADING_STATUS_EVENT"


class Step(BaseModel):
    """
    step 表的一行记录，唯一键 session_id + sub_session_id + step_id
    注意字段顺序要和 fisher 上注册的 pulsar 严格一致
    """

    started_at: int
    ended_at: int
    session_id: str
    sub_session_id: str
    step_id: str
    answer: str
    answer_raw_data: str
    log_data: str
    screen: str
    result: str
    step_type: str


class SubSession(BaseModel):
    """
    sub_session 表的一行记录，唯一键 session_id + sub_session_id
    为了简化逻辑，我们将 session 的字段压扁作为 sub_session 的字段
    注意字段顺序要和 fisher 上注册的 pulsar 严格一致
    """

    started_at: int
    ended_at: int
    module: str
    environment: str
    namespace: TaskNamespace
    session_id: str
    sub_session_id: str
    uin: int
    app_id: str
    device_memory_size: int
    device_model: str
    device_system: str
    device_real_width: int
    device_real_height: int
    device_resized_width: int
    device_resized_height: int
    parameter: str
    from_username: str
    headless_mode: int
    run_mode: str
    result: str
    interrupt: str
    status: str


class ClickHouseService:
    """
    clickhouse 工具类
    """

    def __init__(self):
        self.__thread_pool = TracedThreadPoolExecutor(max_workers=10)
        pulsar_logger = logging.getLogger("pulsar")
        pulsar_logger.handlers.clear()
        pulsar_logger.propagate = True
        self.pulsar_client = pulsar.Client(
            MIDDLEWARE_CONFIG.pulsar_config.service_url,
            authentication=pulsar.AuthenticationToken(
                MIDDLEWARE_CONFIG.pulsar_config.authentication_token
            ),
            logger=pulsar_logger,
        )
        self.pulsar_producer_for_action = self.pulsar_client.create_producer(
            "persistent://wxai/internal/mmfinderdrsandbox_action",
            compression_type=pulsar.CompressionType.ZSTD,
            batching_enabled=True,
            batching_max_publish_delay_ms=500,
        )
        self.pulsar_producer_for_event = self.pulsar_client.create_producer(
            "persistent://wxai/internal/mmfinderdrsandbox_event",
            compression_type=pulsar.CompressionType.ZSTD,
            batching_enabled=True,
            batching_max_publish_delay_ms=500,
        )
        self.pulsar_producer_for_step = self.pulsar_client.create_producer(
            "persistent://wxai/internal/mmfinderdrsandbox_step",
            compression_type=pulsar.CompressionType.ZSTD,
            batching_enabled=True,
            batching_max_publish_delay_ms=500,
        )
        self.pulsar_producer_for_sub_session = self.pulsar_client.create_producer(
            "persistent://wxai/internal/mmfinderdrsandbox_sub_session",
            compression_type=pulsar.CompressionType.ZSTD,
            batching_enabled=True,
            batching_max_publish_delay_ms=500,
        )

    def add_action(self, obj: Action):
        """
        异步给 action 表增加一行记录
        """
        if (
            not obj.session_id
            or not obj.sub_session_id
            or not obj.step_id
            or not obj.action_id
        ):
            logger.error(
                "invalid clickhouse action",
                extra={
                    "customized_data_info": {
                        "data": obj.model_dump_json(),
                    }
                },
            )
        else:
            self.__thread_pool.submit(
                self.__send_pulsar,
                self.pulsar_producer_for_action,
                self.__generate_csv_string_bytes(obj),
            )

    def add_action_decorator(self):
        """
        函数装饰器，用于给 action 表增加一行记录
        """

        def wrapper(func):
            @wraps(func)
            def inner(*args, **kwargs):
                action_type = func.__name__
                if action_type.startswith("__"):
                    action_type = action_type[2:]
                elif action_type.startswith("_"):
                    action_type = action_type[1:]
                started_at = time_util.get_millisecond_timestamp_of_current_time()
                parent_action_id = context_util.get_data_from_context(
                    "sandbox_action_id"
                )
                context_util.set_data_into_context(
                    "sandbox_action_id", context_util.get_span_id()
                )
                step_id = context_util.get_data_from_context("sandbox_step_id")
                create_new_step_flag = False
                if not step_id:
                    step_id = context_util.get_span_id()
                    context_util.set_data_into_context("sandbox_step_id", step_id)
                    create_new_step_flag = True
                res = None
                error_dict = {}
                try:
                    res = func(*args, **kwargs)
                except Exception as e:
                    error_dict = {
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                    raise e
                finally:
                    self.add_action(
                        Action(
                            started_at=started_at,
                            ended_at=time_util.get_millisecond_timestamp_of_current_time(),
                            session_id=context_util.get_trace_id(),
                            sub_session_id=context_util.get_data_from_context(
                                "sandbox_sub_session_id"
                            ),
                            step_id=context_util.get_data_from_context(
                                "sandbox_step_id"
                            ),
                            action_id=context_util.get_data_from_context(
                                "sandbox_action_id"
                            ),
                            parent_action_id=parent_action_id,
                            action_type=action_type,
                            parameter=json.dumps(kwargs, cls=CustomJSONEncoder),
                            result=json.dumps(
                                (res if not error_dict else error_dict),
                                cls=CustomJSONEncoder,
                            ),
                        )
                    )
                    context_util.set_data_into_context(
                        "sandbox_action_id", parent_action_id
                    )
                    if create_new_step_flag:
                        self.add_step(
                            Step(
                                started_at=started_at,
                                ended_at=time_util.get_millisecond_timestamp_of_current_time(),
                                session_id=context_util.get_trace_id(),
                                sub_session_id=context_util.get_data_from_context(
                                    "sandbox_sub_session_id"
                                ),
                                step_id=context_util.get_data_from_context(
                                    "sandbox_step_id"
                                ),
                                answer="[]",
                                answer_raw_data="[]",
                                log_data="[]",
                                screen="[]",
                                result=json.dumps(error_dict, cls=CustomJSONEncoder),
                                step_type=action_type,
                            )
                        )
                        context_util.set_data_into_context("sandbox_step_id", "")

                return res

            return inner

        return wrapper

    @context_util.add_trace_span(
        span_name=EventType.CHECK_SCREENSHOT_LOADING_STATUS_EVENT.value,
        record_output=True,
    )
    def add_event_check_screenshot_loading_status(
        self,
        file: str,
        url: str,
        app_id: str,
        version: str,
        full_path: str,
        image_hash: str,
        hit_page_loaded_image_hash_cache: bool,
        edge_ratio: float,
        edge_ratio_res: bool | None,
        res: bool,
        exception_str: str,
        traceback_str: str,
    ) -> Event:
        """
        添加事件
        """
        return self.__add_event(
            EventType.CHECK_SCREENSHOT_LOADING_STATUS_EVENT,
            {
                "file": file,
                "url": url,
                "app_id": app_id,
                "version": version,
                "full_path": full_path,
                "image_hash": image_hash,
                "hit_page_loaded_image_hash_cache": hit_page_loaded_image_hash_cache,
                "edge_ratio": edge_ratio,
                "edge_ratio_res": edge_ratio_res,
                "res": res,
                "exception": exception_str,
                "traceback": traceback_str,
            },
        )

    def add_step(self, obj: Step):
        """
        异步给 step 表增加一行记录
        """
        if not obj.session_id or not obj.sub_session_id or not obj.step_id:
            logger.error(
                "invalid clickhouse step",
                extra={
                    "customized_data_info": {
                        "data": obj.model_dump_json(),
                    }
                },
            )
        else:
            self.__thread_pool.submit(
                self.__send_pulsar,
                self.pulsar_producer_for_step,
                self.__generate_csv_string_bytes(obj),
            )

    def add_sub_session(self, obj: SubSession):
        """
        异步给 sub_session 表增加一行记录
        """
        if not obj.session_id or not obj.sub_session_id:
            logger.error(
                "invalid clickhouse sub_session",
                extra={
                    "customized_data_info": {
                        "data": obj.model_dump_json(),
                    }
                },
            )
        else:
            self.__thread_pool.submit(
                self.__send_pulsar,
                self.pulsar_producer_for_sub_session,
                self.__generate_csv_string_bytes(obj),
            )

    def flush(self):
        """
        关闭线程池并将缓存中的数据都写入 pulsar 中
        """
        self.__thread_pool.shutdown(wait=True)
        self.pulsar_producer_for_action.flush()
        self.pulsar_producer_for_step.flush()
        self.pulsar_producer_for_sub_session.flush()
        self.pulsar_client.close()

    def __add_event(self, enum: EventType, parameter: dict[str, Any]) -> Event:
        """
        异步给 event 表增加一行记录
        """
        obj = Event(
            started_at=time_util.get_millisecond_timestamp_of_current_time(),
            session_id=context_util.get_trace_id(),
            sub_session_id=context_util.get_data_from_context("sandbox_sub_session_id"),
            step_id=context_util.get_data_from_context("sandbox_step_id"),
            action_id=context_util.get_data_from_context("sandbox_action_id"),
            event_type=enum.value,
            parameter=json.dumps(parameter),
        )
        if not obj.session_id or not obj.sub_session_id:
            logger.error(
                "invalid clickhouse event",
                extra={
                    "customized_data_info": {
                        "data": obj.model_dump_json(),
                    }
                },
            )
        else:
            self.__thread_pool.submit(
                self.__send_pulsar,
                self.pulsar_producer_for_event,
                self.__generate_csv_string_bytes(obj),
            )
        return obj

    def __generate_csv_string_bytes(self, obj: BaseModel) -> bytes:
        obj_dict = obj.model_dump()
        obj_list_data = [time_util.get_millisecond_timestamp_of_current_time() // 1000]
        for _, value in obj_dict.items():
            obj_list_data.append(value)
        output = io.StringIO()
        csv_writer = csv.writer(output)
        csv_writer.writerow(obj_list_data)
        csv_string = output.getvalue()
        output.close()
        return csv_string.encode("utf-8")

    def __send_pulsar(self, producer: pulsar.Producer, data: bytes):
        """
        发送数据到 Pulsar
        """
        try:
            producer.send(data)
        except Exception as e:  # pylint: disable=W0718
            logger.error(
                "向 Pulsar 写入数据失败",
                extra={
                    "customized_data_info": {
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )


clickhouse_service = ClickHouseService()
