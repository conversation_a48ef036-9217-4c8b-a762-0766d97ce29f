"""
沙箱操作的核心模块
"""

import base64
import copy
import json
import queue
import time
import traceback
import uuid
from typing import Any

from wxms.config.config import STANDARD_INPUT_CONFIG
from wxms.logger import logger
from wxms.model.exception.error_code_exception import AppletInterruptError
from wxms.model.service.sandbox_service import (
    APPLET_FIXED_HEIGHT,
    APPLET_FIXED_WIDTH,
    SandboxActionMessage,
    SandboxActionStandardConfig,
    SandboxActionStandardOutput,
    ScreenshotDTO,
    TaskNamespace,
)
from wxms.service.clickhouse_service import clickhouse_service
from wxms.service.image_service import image_service
from wxms.service.js_service import js_service
from wxms.service.sandbox_util_service import sandbox_util_service
from wxms.util.common_util import common_util
from wxms.util.context_util import TracedThreadPoolExecutor, context_util
from wxms.util.httpx_util import httpx_util


# 无头小程序中特有的 XWeb 事件
# XWeb.GetAllElementsRects
# XWeb.GetFilteredDomXml
# XWeb.NavigateBack
# XWeb.RouteToPage
# XWeb.ShareAppMessage
# XWeb.Sleep
class WXMSPage:
    """
    小程序的操作
    """

    def __init__(
        self,
        headless_mode: int,
        target_id: str,
    ):
        self.headless_mode = headless_mode
        self.target_id = target_id

        self.real_size = (-1, -1)
        self.resized_size = (APPLET_FIXED_WIDTH, APPLET_FIXED_HEIGHT)
        self.touch_event_id = 0

    def dom_enable_cdp_list(self) -> list[dict]:
        """
        生成相应操作的 cdp 列表
        https://chromedevtools.github.io/devtools-protocol/tot/DOM/#method-enable
        启用 DOM 代理，无头小程序是移动客户端所以不需要做处理
        """
        res = []
        if not self.headless_mode:
            res = [
                {
                    "target_id": self.target_id,
                    "method": "DOM.enable",
                    "params": {
                        "task_id": str(uuid.uuid4()),
                    },
                }
            ]
        return res

    def get_all_elements_rects_with_offset_cdp_list(self) -> list[dict]:
        """
        生成相应操作的 cdp 列表
        """
        if self.headless_mode:
            return [
                {
                    "target_id": self.target_id,
                    "method": "XWeb.GetAllElementsRects",
                    "params": {
                        "task_id": str(uuid.uuid4()),
                    },
                }
            ]
        else:
            return [
                js_service.get_sandbox_js_api_runtime_evaluate_params(
                    target_id=self.target_id,
                    js_method="getAllElementsRectsWithOffset()",
                ),
                js_service.get_sandbox_js_api_runtime_evaluate_params(
                    target_id=self.target_id,
                    js_method="getAllNativeElementsRects()",
                ),
            ]

    def get_all_elements_rects_with_offset_parse_result(
        self, cdp_response_list: list[dict]
    ) -> tuple[dict, dict]:
        """
        相应操作的结果处理
        获取当前页面所有元素的框矩形信息，矩形信息会带上 webview 的偏移量，从而保证坐标原点在最左上角

        :return: 出参格式不管是不是无头小程序都要保持一致，第一个参数是非原生的列表，第二个是原生的列表
        """
        if self.headless_mode:
            resp = cdp_response_list[0]
            elements_rects_list = []
            native_elements_rects_list = []
            res_code = resp.get("code", 0)
            if res_code == 0:
                # 非原生元素，需要带上偏移量
                temp_str = (
                    resp.get("data", {}).get("data", {}).get("elements_rects", "")
                )
                elements_rects_list = json.loads(temp_str) if temp_str else []
                elements_rects_list = [
                    {
                        "isInteractive": x.get("isInteractive", True),
                        "listeners": x.get("listeners", []),  # h5
                        "rect": {k: int(v) for k, v in x["rect"].items()},
                        "tagName": x["tagName"],
                        "wxEvents": x.get("wxEvents", []),  # 小程序
                        "xpath": x.get("xpath", ""),
                    }
                    for x in elements_rects_list
                ]
                webview_offset_y = (
                    resp.get("data", {}).get("data", {}).get("webview_offsetY", 0)
                )
                for rect in elements_rects_list:
                    rect["rect"]["y"] += webview_offset_y
                # 原生元素，自动带上偏移量，无需额外添加
                temp_str = (
                    resp.get("data", {})
                    .get("data", {})
                    .get("native_elements_rects", "")
                )
                native_elements_rects_list = json.loads(temp_str) if temp_str else []
                native_elements_rects_list = [
                    {
                        "isInteractive": x.get("isInteractive", True),
                        "listeners": x.get("listeners", []),  # h5
                        "rect": {k: int(v) for k, v in x["rect"].items()},
                        "tagName": x["tagName"],
                        "wxEvents": x.get("wxEvents", []),  # 小程序
                        "xpath": x.get("xpath", ""),
                    }
                    for x in native_elements_rects_list
                ]
            elements_rects = self.__mock_cdp_resp(res_code, elements_rects_list)
            native_elements_rects = self.__mock_cdp_resp(
                res_code, native_elements_rects_list
            )
            res = elements_rects, native_elements_rects
        else:
            elements_rects = cdp_response_list[0]
            native_elements_rects = cdp_response_list[1]
            res = elements_rects, native_elements_rects
        return (
            self.__adjust_rects_cdp_resp(res[0]),
            self.__adjust_rects_cdp_resp(res[1]),
        )

    def get_dom_xml_cdp_list(self, include_offscreen_elements=False) -> list[dict]:
        """
        生成相应操作的 cdp 列表
        获取当前页面的 dom xml

        :param include_offscreen_elements: 是否包含离屏元素
        """
        if self.headless_mode:
            return [
                {
                    "target_id": self.target_id,
                    "method": "XWeb.GetFilteredDomXml",
                    "params": {
                        "task_id": str(uuid.uuid4()),
                        "role": "server",
                        "include_offscreen_elements": include_offscreen_elements,
                    },
                }
            ]
        else:
            return [
                js_service.get_sandbox_js_api_runtime_evaluate_params(
                    target_id=self.target_id, js_method="getDomXml()"
                )
            ]

    def get_dom_xml_parse_result(self, cdp_response_list: list[dict]) -> dict:
        """
        相应操作的结果处理
        获取当前页面的 dom xml

        :return: 出参格式不管是不是无头小程序都要保持一致
        """
        resp = cdp_response_list[0]
        if self.headless_mode:
            xml = ""
            res_code = resp.get("code", 0)
            if res_code == 0:
                data = resp.get("data", {}).get("data", {})
                dom_xml = data.get("dom_xml", "")
                native_xml = data.get("native_xml", "")
                dom_xml_value = (
                    "<html></html>"
                    if dom_xml == ""
                    else js_service.decode_csv_to_xml(dom_xml)
                )
                xml = f"""<page>
    <iframe>
        {dom_xml_value}
    </iframe>
    {native_xml}
</page>
"""
            return self.__mock_cdp_resp(res_code, xml)
        else:
            return resp

    def simple_click_left_by_xpath_cdp_list(self, xpath: str) -> list[dict]:
        """
        生成相应操作的 cdp 列表
        左键点击操作，入参是 xpath 而不是坐标

        :param xpath: 被点击元素的 xpath
        """
        if self.headless_mode:
            touch_event_id = self.__get_touch_event_id()
            return [
                {
                    "target_id": self.target_id,
                    "method": "Input.dispatchTouchEvent",
                    "params": {
                        "task_id": str(uuid.uuid4()),
                        "type": "touchStart",
                        "id": touch_event_id,
                        "touchXpath": xpath,
                    },
                },
                {
                    "target_id": self.target_id,
                    "method": "Input.dispatchTouchEvent",
                    "params": {
                        "task_id": str(uuid.uuid4()),
                        "type": "touchEnd",
                        "id": touch_event_id,
                        "touchXpath": xpath,
                    },
                },
            ]
        elif "native" in xpath:
            return [
                js_service.get_sandbox_js_api_runtime_evaluate_params(
                    target_id=self.target_id,
                    js_method=f"operateNativeElement({{ event: 'click', xpath: '{xpath}' }})",
                )
            ]
        else:
            logger.error(
                "沙箱不支持非原生元素的点击",
                extra={
                    "customized_data_info": {
                        "xpath": xpath,
                    }
                },
            )
            return []

    def simple_click_left_cdp_list(self, x: int, y: int) -> list[dict]:
        """
        生成相应操作的 cdp 列表
        左键点击操作，使用 touch 事件而不是鼠标事件来模拟，因为触摸屏没有鼠标

        :param x: 点击的横坐标
        :param y: 点击的纵坐标
        """
        touch_event_id = self.__get_touch_event_id()
        # 转换为设备上的真实坐标
        # 截图的时候会将设备的屏幕尺寸固定缩放至 APPLET_FIXED_WIDTH * APPLET_FIXED_HEIGHT 的框中
        # 从而导致视觉模型推理结果的坐标不是设备上的真实坐标
        # 所以如果是固定尺寸坐标系上的坐标，则需要转换成设备上的真实坐标
        real_xy = sandbox_util_service.translate_coordinate_from_fixed_to_real(
            (x, y),
            self.real_size,
            self.resized_size,
        )
        touch_points_real = [
            {
                "x": real_xy[0],
                "y": real_xy[1],
                "id": touch_event_id,
            }
        ]
        return [
            {
                "target_id": self.target_id,
                "method": "Input.dispatchTouchEvent",
                "params": {
                    "task_id": str(uuid.uuid4()),
                    "type": "touchStart",
                    "touchPoints": touch_points_real,
                },
            },
            {
                "target_id": self.target_id,
                "method": "Input.dispatchTouchEvent",
                "params": {
                    "task_id": str(uuid.uuid4()),
                    "type": "touchEnd",
                    "touchPoints": touch_points_real,
                },
            },
        ]

    def simple_paste_text_cdp_list(self, text: str) -> list[dict]:
        """
        生成相应操作的 cdp 列表
        https://chromedevtools.github.io/devtools-protocol/tot/Input/#method-insertText

        :param text: 需要粘贴输入的文本
        """
        params: dict[str, Any] = {
            "task_id": str(uuid.uuid4()),
            "text": text,
        }
        if self.headless_mode:
            params["max_waiting_time_ms"] = 1000
        return [
            {
                "target_id": self.target_id,
                "method": "Input.insertText",
                "params": params,
            }
        ]

    def simple_press_enter_cdp_list(self) -> list[dict]:
        """
        生成相应操作的 cdp 列表
        按下并释放回车键
        """
        return [
            {
                "target_id": self.target_id,
                "method": "Input.dispatchKeyEvent",
                "params": {
                    "task_id": str(uuid.uuid4()),
                    "type": "keyDown",
                    "key": "Enter",
                    "keyCode": 0x0D,
                    "windowsVirtualKeyCode": 0x0D,
                    "code": "Enter",
                    "unmodifiedText": "\n",
                    "text": "\n",
                },
            },
            {
                "target_id": self.target_id,
                "method": "Input.dispatchKeyEvent",
                "params": {
                    "task_id": str(uuid.uuid4()),
                    "type": "keyUp",
                    "key": "Enter",
                    "keyCode": 0x0D,
                    "windowsVirtualKeyCode": 0x0D,
                    "code": "Enter",
                    "unmodifiedText": "\n",
                    "text": "\n",
                },
            },
        ]

    def simple_screenshot_cdp_list(self) -> list[dict]:
        """
        生成相应操作的 cdp 列表
        """
        params = {
            "task_id": str(uuid.uuid4()),
        }
        if self.headless_mode:
            params["type"] = "wxam"
        return [
            {
                "target_id": self.target_id,
                "method": "Page.captureScreenshot",
                "params": params,
            }
        ]

    def simple_screenshot_parse_result(
        self,
        cdp_response_list: list[dict],
        save_path: str,
    ):
        """
        相应操作的结果处理
        获取当前页面的截图（会更新设备对应尺寸信息）

        :param save_path: 图片保存路径
        """
        # 1. 获取截图的 base64 数据
        screenshot_response = cdp_response_list[0]
        image_bytes_field = "result"
        if self.headless_mode:
            image_bytes_field = "data"
        capsule_rect: None | dict[str, int] = (
            screenshot_response.get("data", {})
            .get(image_bytes_field, {})
            .get("capsule_rect", None)
        )
        image_base64 = (
            screenshot_response.get("data", {})
            .get(image_bytes_field, {})
            .get("data", "")
        )
        if image_base64 == "":
            raise ValueError(f"Failed to capture screenshot: {screenshot_response}")
        # 2. 解码为二进制数据
        image_bytes = base64.b64decode(image_base64)
        if self.headless_mode:
            # wxam 解码
            wxam_info = image_service.wxam_get_info(image_bytes)
            image_bytes = image_service.wxam_convert_to_pic(
                wxam_data=image_bytes,
                output_type=0,  # jpg
                buf_size=wxam_info.width * wxam_info.height * wxam_info.frame_count * 2,
            )
        # 3. 保存图片并更新设备对应尺寸信息
        self.real_size, self.resized_size = image_service.image_save(
            save_path, image_bytes, capsule_rect
        )

    def simple_scroll_by_xpath_cdp_list(
        self, xpath: str, delta_x: int, delta_y: int
    ) -> list[dict]:
        """
        生成相应操作的 cdp 列表
        滚动操作，入参是 xpath 而不是坐标

        :param xpath: 被滚动元素的 xpath
        :param delta_x: 水平滚动距离，越左边的坐标越小
        :param delta_y: 垂直滚动距离，越上边的坐标越小
        """
        if self.headless_mode:
            touch_event_id = self.__get_touch_event_id()
            return [
                {
                    "target_id": self.target_id,
                    "method": "Input.dispatchTouchEvent",
                    "params": {
                        "task_id": str(uuid.uuid4()),
                        "type": "touchStart",
                        "id": touch_event_id,
                        "touchXpath": xpath,
                        "touchXpathDelta": {
                            "x": 0,
                            "y": 0,
                        },
                    },
                },
                {
                    "target_id": self.target_id,
                    "method": "Input.dispatchTouchEvent",
                    "params": {
                        "task_id": str(uuid.uuid4()),
                        "type": "touchMove",
                        "id": touch_event_id,
                        "touchXpath": xpath,
                        "touchXpathDelta": {
                            "x": delta_x,
                            "y": delta_y,
                        },
                    },
                },
                {
                    "target_id": self.target_id,
                    "method": "Input.dispatchTouchEvent",
                    "params": {
                        "task_id": str(uuid.uuid4()),
                        "type": "touchEnd",
                        "id": touch_event_id,
                        "touchXpath": xpath,
                        "touchXpathDelta": {
                            "x": delta_x,
                            "y": delta_y,
                        },
                    },
                },
            ]
        else:
            logger.error(
                "沙箱不支持 xpath 的 scroll",
                extra={
                    "customized_data_info": {
                        "xpath": xpath,
                        "delta_x": delta_x,
                        "delta_y": delta_y,
                    }
                },
            )
            return []

    def simple_scroll_cdp_list(
        self,
        coordinate: tuple[int, int],
        delta_x: int,
        delta_y: int,
    ) -> list[dict]:
        """
        生成相应操作的 cdp 列表
        滚动页面

        :param coordinate: 滚动起点的坐标 (x, y)
        :param delta_x: 水平滚动距离，越左边的坐标越小
        :param delta_y: 垂直滚动距离，越上边的坐标越小
        """
        # 转换为设备上的真实坐标
        real_xy = sandbox_util_service.translate_coordinate_from_fixed_to_real(
            coordinate,
            self.real_size,
            self.resized_size,
        )
        x = real_xy[0]
        y = real_xy[1]
        delta_x = int(delta_x * self.real_size[0] / self.resized_size[0])
        delta_y = int(delta_y * self.real_size[1] / self.resized_size[1])
        touch_event_id = self.__get_touch_event_id()
        data_list = [
            {
                "target_id": self.target_id,
                "method": "Input.dispatchTouchEvent",
                "params": {
                    "task_id": str(uuid.uuid4()),
                    "type": "touchStart",  # 事件类型（touchStart/touchMove/touchEnd）
                    "touchPoints": [{"x": x, "y": y, "id": touch_event_id}],
                },
            },
            {
                "target_id": self.target_id,
                "method": "Input.dispatchTouchEvent",
                "params": {
                    "task_id": str(uuid.uuid4()),
                    "type": "touchMove",  # 事件类型（touchStart/touchMove/touchEnd）
                    "touchPoints": [
                        {"x": x + delta_x, "y": y + delta_y, "id": touch_event_id}
                    ],
                },
            },
            {
                "target_id": self.target_id,
                "method": "Input.dispatchTouchEvent",
                "params": {
                    "task_id": str(uuid.uuid4()),
                    "type": "touchEnd",
                    "touchPoints": [
                        {"x": x + delta_x, "y": y + delta_y, "id": touch_event_id}
                    ],
                },
            },
        ]
        return data_list

    def __adjust_rects_cdp_resp(self, resp: dict) -> dict:
        res = copy.deepcopy(resp)
        temp_list = (
            res.get("data", {}).get("result", {}).get("result", {}).get("value", [])
        )
        for temp in temp_list:
            rect = temp.get("rect", {})
            rect_x = rect.get("x", 0)
            rect_y = rect.get("y", 0)
            rect_width = rect.get("width", 0)
            rect_height = rect.get("height", 0)
            (rect["x"], rect["y"]), rect["width"], rect["height"] = (
                sandbox_util_service.translate_rect_from_real_to_fixed(
                    (rect_x, rect_y),
                    rect_width,
                    rect_height,
                    self.real_size,
                    self.resized_size,
                )
            )
        return res

    def __get_touch_event_id(self) -> int:
        self.touch_event_id += 1
        return self.touch_event_id

    def __mock_cdp_resp(self, res_code: int, value: Any) -> dict:
        return {"code": res_code, "data": {"result": {"result": {"value": value}}}}


class WXMS:
    """
    微信的操作
    """

    def __init__(
        self,
        base_url: str,
        link_type: str,
        uin: int,
        from_username: str,
        username: str,
        request_id: str,
        headless_mode: int,
    ):
        self.base_url = base_url
        self.link_type = link_type
        self.uin = uin
        self.from_username = from_username
        self.username = username
        self.request_id = request_id
        self.headless_mode = headless_mode
        self.stop = False

    def close(self, app_id: str, close_applet: bool, keep_session: bool):
        """
        释放微信实例。当 WXMS 不再使用时，必须调用该方法，释放远端沙箱资源

        :param app_id: 小程序的 id
        :param close_applet: 是否关闭小程序
        """
        self.stop = True
        if self.headless_mode and close_applet:
            # TODO 后续不管是不是无头都要关闭
            try:
                self.__xweb_close_applet(app_id, keep_session)
            except AppletInterruptError:
                pass

    def simple_get_app(
        self, app_id: str, entry_url: str, skip_launch_applet: bool
    ) -> WXMSPage:
        """
        启动指定的小程序，并返回一个 WXMSPage 对象用于与页面交互

        :param app_id: 小程序的 id
        :param entry_url: 启动时小程序的目标页面路径 (如: pages/Mine/Mine.html)
        :param skip_launch_applet: 是否跳过启动小程序
        :return: WXMSPage 对象
        """
        wxms_page_params = {
            "headless_mode": self.headless_mode,
            "target_id": app_id,
        }
        # 1. 在非无头且没跳过启动小程序的场景下，需要关闭已打开的小程序
        if not self.headless_mode and not skip_launch_applet:
            targets = self.__xweb_targets()
            for target in targets["data"]["data"]:
                if "url" in target and app_id in target["url"]:
                    self.__xweb_close_applet(app_id, False)
                    logger.info(
                        "close existed applet success",
                        extra={
                            "customized_data_info": {
                                "app_id": app_id,
                            }
                        },
                    )
                    time.sleep(1)
                    break
        # 2. 启动指定的小程序
        if not skip_launch_applet:
            xweb_launch_applet_resp = self.__xweb_launch_applet(
                app_id=app_id, entry_url=entry_url
            )
            logger.info(
                "get launch applet result",
                extra={
                    "customized_data_info": {
                        "app_id": app_id,
                        "entry_url": entry_url,
                        "xweb_launch_applet_resp": xweb_launch_applet_resp,
                    }
                },
            )
            if xweb_launch_applet_resp.get("code", -1) != 0:
                raise RuntimeError("launch applet failed")
            # time.sleep(5)
        # 3. 非无头场景下，需要更新 target_id 信息
        if not self.headless_mode:
            target_id = ""
            targets = self.__xweb_targets()
            for target in targets["data"]["data"]:
                if "url" in target and app_id in target["url"]:  # 根据 URL 匹配
                    target_id = target["id"]
                    break
            if target_id:
                wxms_page_params["target_id"] = target_id
            else:
                raise ValueError(f"no target found for app_id={app_id}")
        return WXMSPage(**wxms_page_params)

    def xweb_get_applet_page_info_cdp_list(self, app_id: str) -> list[dict]:
        """
        生成相应操作的 cdp 列表
        获取已打开的小程序的当前页面信息
        回包中的字段 device_info 中的 model 格式为 Generation<Identifier>
        例如 iPhone 16 Pro<iPhone17,1> 表示这是第 17 代 iPhone 中的第一个型号，即 iPhone 16 Pro
        更多完整型号可以参考：https://theapplewiki.com/wiki/Models

        :param app_id: 小程序的 id
        """
        return [
            {
                "method": "XWeb.GetAppletPageInfo",
                "params": {
                    "task_id": str(uuid.uuid4()),
                    "role": "server",
                    "app_id": app_id,
                },
            }
        ]

    def xweb_navigate_back(self, back_num: int, target_url: str) -> dict:
        """
        返回到前 back_num 个的小程序页面

        :param back_num: 返回的页面数
        :param target_url: 回退只适用于页面右滑或左上角有返回按钮的页面，通过 tabbar 跳转的页面是不适用的
            为了在 tabbar 跳转的页面也能正常工作，使用 target_url 兜底
        """
        if not self.headless_mode:
            return {}
        return self.__cdp_proxy(
            [
                {
                    "method": "XWeb.NavigateBack",
                    "params": {
                        "task_id": str(uuid.uuid4()),
                        "role": "server",
                        "back_num": back_num,
                        "target_url": target_url,
                    },
                }
            ]
        )

    def xweb_route_to_page(self, app_id: str, url: str) -> dict:
        """
        跳转小程序到指定页面

        :param app_id: 小程序的 id
        :param url: 小程序的目标页面路径 (如: pages/Mine/Mine.html)
        """
        if not self.headless_mode:
            return {}
        return self.__cdp_proxy(
            [
                {
                    "method": "XWeb.RouteToPage",
                    "params": {
                        "task_id": str(uuid.uuid4()),
                        "role": "server",
                        "app_id": app_id,
                        "url": url,
                    },
                }
            ]
        )

    def xweb_sleep_cdp_list(self, milliseconds: int, force: bool = False) -> list[dict]:
        """
        生成睡眠的 cdp 指令，无头的操作由于只在操作结束后才返回所以睡眠只需要千分之一

        :param milliseconds: 睡眠时间，单位毫秒
        :param force: 是否绕过无头的判断逻辑
        :return: cdp 指令列表
        """
        if self.headless_mode and not force:
            milliseconds = milliseconds // 1000
        if milliseconds <= 0:
            milliseconds = 1
        return [
            {
                "method": "XWeb.Sleep",
                "params": {
                    "role": "server",
                    "task_id": str(uuid.uuid4()),
                    "sleep_ms": milliseconds,
                },
            }
        ]

    def __cdp_proxy(self, data_list: list[dict]) -> dict:
        if self.headless_mode:
            return self.__cdp_proxy_headless(data_list)
        else:
            res = {}
            for data in data_list:
                # TODO 后续 http 的也要支持多个 cdp 指令的调用下发
                res = self.__cdp_proxy_http(data)
            return res

    def __cdp_proxy_headless(self, data_list: list[dict]) -> dict:
        return sandbox_util_service.base_cdp_proxy_headless(
            link_type=self.link_type,
            from_uin=1111,
            to_uin=self.uin,
            from_username=self.from_username,
            to_username=self.username,
            request_id=self.request_id,
            data=data_list,
        )[0]

    def __cdp_proxy_http(self, data: dict) -> dict:
        response = httpx_util.send_request(
            method="POST",
            url=f"{self.base_url}/v1/sandbox/cdp_proxy/{self.uin}",
            json=data,
            timeout=10,
            span_name=data.get("method", None),
        )
        logger.debug(
            "get cdp proxy http response",
            extra={
                "customized_data_info": {
                    "text": response.text,
                    "status_code": response.status_code,
                }
            },
        )
        return response.json()

    def __xweb_close_applet(self, app_id: str, keep_session: bool):
        params: dict[str, Any] = {
            "task_id": str(uuid.uuid4()),
            "role": "server",
            "app_id": app_id,
        }
        if self.headless_mode and keep_session:
            params["keep_session"] = True
        self.__cdp_proxy(
            [
                {
                    "method": "XWeb.CloseApplet",
                    "params": params,
                }
            ]
        )

    def __xweb_launch_applet(self, app_id: str, entry_url: str) -> dict:
        data = {
            "method": "XWeb.LaunchApplet",
            "params": {
                "task_id": str(uuid.uuid4()),
                "role": "server",
                "app_id": app_id,
            },
        }
        if self.headless_mode == 0:
            data["params"]["entry_url"] = entry_url
            data["params"]["width"] = APPLET_FIXED_WIDTH
            data["params"]["height"] = APPLET_FIXED_HEIGHT
        else:
            data["params"]["entry_url"] = ""
            if self.headless_mode == 1:
                data["params"]["headless"] = 1
            if app_id == "wx6c03ed6dfa30c735":
                data["params"]["prefer_hot_launch"] = True
        resp = self.__cdp_proxy([data])
        if self.headless_mode and entry_url:
            time.sleep(1)  # 等待小程序启动
            self.xweb_route_to_page(app_id, entry_url)
        return resp

    def __xweb_targets(self) -> dict:
        # 无头小程序中不支持该操作
        return self.__cdp_proxy(
            [
                {
                    "method": "XWeb.targets",
                    "params": {
                        "task_id": str(uuid.uuid4()),
                        "role": "server",
                    },
                }
            ]
        )


class SandboxClient:
    """
    沙箱客户端类
    """

    def __init__(
        self,
        namespace: TaskNamespace,
        base_url: str,
        app_id: str,
        app_entry_url: str,
        uin: int,
        from_username: str,
        username: str,
        headless_mode: int,
        standard_start_config: SandboxActionStandardConfig | None,
        check_screenshot_loading_status_max_retry_count: int,
        check_screenshot_loading_status_max_retry_count_for_back: int,
        is_async_action_result: bool,
        link_type: str,
        skip_launch_applet: bool,
        sleep_seconds_before_screenshot: float,
    ):
        # 底层基建的变量
        self.request_id = context_util.get_trace_id(mock_fake_trace_id=True)
        self.standard_action_count = 0
        self.start_interrupt = None
        self.stop_flag = False
        self.__check_screenshot_loading_status_max_retry_count = (
            check_screenshot_loading_status_max_retry_count
        )
        self.__check_screenshot_loading_status_max_retry_count_for_back = (
            check_screenshot_loading_status_max_retry_count_for_back
        )
        self.__is_async_action_result = is_async_action_result
        self.__sleep_seconds_before_screenshot = sleep_seconds_before_screenshot
        self.__processing_action_result = False
        self.__image_loading_status_signal_queue: queue.Queue[tuple[str, str]] = (
            queue.Queue()
        )
        self.__upload_image_queue: queue.Queue[tuple[str, str, str]] = queue.Queue()
        self.__thread_pool = TracedThreadPoolExecutor(max_workers=10)
        self.__screenshot_file_dir = image_service.get_image_file_dir(
            app_id=app_id, uin=uin
        )
        self.__screenshot_object_key_dir = ""
        self.__screenshot_object_key_dir_future = self.__thread_pool.submit(
            image_service.get_image_object_key_dir,
            namespace=namespace,
            app_id=app_id,
            uin=uin,
            request_id=self.request_id,
        )
        self.__thread_pool.submit(self.__upload_image_worker)
        # 小程序相关的变量
        self.__wxms_instance = WXMS(
            base_url=base_url,
            link_type=link_type,
            uin=uin,
            from_username=from_username,
            username=username,
            request_id=self.request_id,
            headless_mode=headless_mode,
        )
        self.namespace = namespace
        self.app_id = app_id
        self.app_entry_url = app_entry_url
        self.target_id = ""
        self.standard_output_list: list[SandboxActionStandardOutput] = []
        self.long_img_url = ""
        self.long_unique_img_url = ""
        try:
            self.__standard_start(
                config=standard_start_config,
                skip_launch_applet=skip_launch_applet,
            )
        except AppletInterruptError as e:
            # 有可能开始就中断了
            self.standard_end(close_applet=True, keep_session=False)
            self.start_interrupt = e.params  # type: ignore
        except Exception as e:  # pylint: disable=W0718
            self.standard_end(close_applet=True, keep_session=False)
            logger.error(
                "standard_start error，standard_end success",
                extra={
                    "customized_data_info": {
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )

    def get_image_loading_status_signal(self, signal_key: str) -> tuple[str, bool]:
        """
        获取图片 loading 状态判断结果的信号，空信号表示图片状态正常，反之则返回下一张图本地文件的路径
        如果不是异步处理 action 结果或没有处理中的 action 结果则直接返回空信号

        :param signal_key: 需要的信号，通常是图片本地文件的路径或者是 final
            final 信号表示上一操作的所有数据已经处理完毕，所以获取 final 信号时会将 self.__processing_action_result 设置为 False
        :return: 信号结果，是否遇到 error 信号
        """
        res: None | str = None
        get_error_signal = False
        if not self.__is_async_action_result or not self.__processing_action_result:
            res = ""
        while res is None:
            temp_signal_key, temp_res = self.__image_loading_status_signal_queue.get()
            if temp_signal_key == signal_key:
                res = temp_res
            if temp_signal_key == "error" and signal_key != "final":
                res = temp_res
                get_error_signal = True
                break
        if signal_key == "final":
            self.__processing_action_result = False
        return res, get_error_signal

    @property
    def screenshot_object_key_dir(self):
        """
        存储图片在对象存储上的 object key 文件夹，不以 / 结尾
        """
        if not self.__screenshot_object_key_dir:
            self.__screenshot_object_key_dir = (
                self.__screenshot_object_key_dir_future.result()
            )
        return self.__screenshot_object_key_dir

    @context_util.add_trace_span(record_input=True)
    @clickhouse_service.add_action_decorator()
    def standard_click(
        self,
        *,
        coordinate: tuple[int, int] | None = None,
        xpath: str | None = None,
        click_times: int = 1,
        config: SandboxActionStandardConfig | None = None,
    ) -> SandboxActionStandardOutput:
        """
        标准的点击操作

        :param coordinate: 点击的坐标 (x, y)
        :param xpath: 被点击元素的 xpath
        :param click_times: 点击的次数
        :return: 标准操作的输出
        """
        if config is None:
            config = SandboxActionStandardConfig()
        self.__send_action_message_list(
            action_message_list=config.action_message_list_before_action
        )
        commands = []
        if coordinate is not None:
            for i in range(click_times):
                commands.extend(
                    self.__wxms_page_instance.simple_click_left_cdp_list(
                        coordinate[0], coordinate[1]
                    )
                )
                if i != click_times - 1:
                    commands.extend(
                        self.__wxms_instance.xweb_sleep_cdp_list(
                            milliseconds=200, force=True
                        )
                    )
        elif xpath is not None:
            for i in range(click_times):
                commands.extend(
                    self.__wxms_page_instance.simple_click_left_by_xpath_cdp_list(xpath)
                )
                if i != click_times - 1:
                    commands.extend(
                        self.__wxms_instance.xweb_sleep_cdp_list(
                            milliseconds=200, force=True
                        )
                    )
        else:
            raise ValueError("both coordinate and xpath are None")
        self.__cdp_proxy(commands)
        return self.__process_standard_result(
            screenshot_file_name=self.__get_screenshot_file_name(
                self.standard_click.__name__
            ),
            config=config,
        )

    @context_util.add_trace_span(record_input=True)
    @clickhouse_service.add_action_decorator()
    def standard_end(
        self,
        *,
        close_applet: bool,
        keep_session: bool,
        config: SandboxActionStandardConfig | None = None,
    ):
        """
        标准的结束操作

        :param close_applet: 是否关闭小程序
        """
        if self.stop_flag:
            # 幂等结束
            return
        if config is None:
            config = SandboxActionStandardConfig()
        concat_images_vertically_future = self.__thread_pool.submit(
            image_service.concat_images_vertically,
            [i.screenshot_dto for i in self.standard_output_list],
        )
        self.__send_action_message_list(
            action_message_list=config.action_message_list_before_action
        )
        # 注意 stop_flag 的位置
        self.stop_flag = True
        self.__wxms_instance.close(
            app_id=self.app_id,
            close_applet=close_applet,
            keep_session=keep_session,
        )
        self.long_img_url, self.long_unique_img_url = (
            concat_images_vertically_future.result()
        )
        self.__thread_pool.shutdown(wait=True)

    @context_util.add_trace_span(record_input=True)
    @clickhouse_service.add_action_decorator()
    def standard_input(
        self,
        *,
        text: str,
        coordinate: tuple[int, int] | None = None,
        xpath: str | None = None,
        config: SandboxActionStandardConfig | None = None,
    ) -> SandboxActionStandardOutput:
        """
        标准的输入操作

        :param text: 需要粘贴输入的文本
        :param coordinate: 输入框的坐标 (x, y)
        :param xpath: 输入框元素的 xpath
        :return: 标准操作的输出
        """
        if config is None:
            config = SandboxActionStandardConfig()
        self.__send_action_message_list(
            action_message_list=config.action_message_list_before_action
        )
        commands = []
        if coordinate is not None:
            commands.extend(
                self.__wxms_page_instance.simple_click_left_cdp_list(
                    coordinate[0], coordinate[1]
                )
            )
        elif xpath is not None:
            commands.extend(
                self.__wxms_page_instance.simple_click_left_by_xpath_cdp_list(xpath)
            )
        else:
            raise ValueError("both coordinate and xpath are None")
        applet_page_info = self.standard_output_list[-1].applet_page_info
        app_id = applet_page_info.get("data", {}).get("data", {}).get("app_id", "")
        path = applet_page_info.get("data", {}).get("data", {}).get("path", "")
        key = f"{app_id}---{path}"
        if app_id and key in STANDARD_INPUT_CONFIG.sleep_before_input_dict:
            commands.extend(
                self.__wxms_instance.xweb_sleep_cdp_list(
                    STANDARD_INPUT_CONFIG.sleep_before_input_dict[key], True
                )
            )
        commands.extend(self.__wxms_page_instance.simple_paste_text_cdp_list(text))
        commands.extend(self.__wxms_instance.xweb_sleep_cdp_list(350, True))
        commands.extend(self.__wxms_page_instance.simple_press_enter_cdp_list())
        self.__cdp_proxy(commands)
        return self.__process_standard_result(
            screenshot_file_name=self.__get_screenshot_file_name(
                self.standard_input.__name__
            ),
            config=config,
        )

    @context_util.add_trace_span(record_input=True)
    @clickhouse_service.add_action_decorator()
    def standard_navigate_back(
        self, *, back_num: int, config: SandboxActionStandardConfig | None = None
    ) -> SandboxActionStandardOutput:
        """
        标准的返回操作

        :param back_num: 返回的页面数
        :return: 标准操作的输出
        """
        if config is None:
            config = SandboxActionStandardConfig()
        self.__send_action_message_list(
            action_message_list=config.action_message_list_before_action
        )
        self.__xweb_navigate_back(back_num)
        return self.__process_standard_result(
            screenshot_file_name=self.__get_screenshot_file_name(
                self.standard_navigate_back.__name__
            ),
            config=config,
        )

    @context_util.add_trace_span(record_input=True)
    @clickhouse_service.add_action_decorator()
    def standard_scroll(
        self,
        *,
        coordinate: tuple[int, int] | None = None,
        xpath: str | None = None,
        delta_x: int = 0,
        delta_y: int = 0,
        config: SandboxActionStandardConfig | None = None,
    ) -> SandboxActionStandardOutput:
        """
        标准的滚动操作

        :param coordinate: 滚动起点的坐标 (x, y)
        :param xpath: 滚动起点元素的 xpath
        :param delta_x: 水平滚动距离，越左边的坐标越小
        :param delta_y: 垂直滚动距离，越上边的坐标越小
        :return: 标准操作的输出
        """
        if config is None:
            config = SandboxActionStandardConfig()
        self.__send_action_message_list(
            action_message_list=config.action_message_list_before_action
        )
        commands = []
        if coordinate is not None:
            commands = self.__wxms_page_instance.simple_scroll_cdp_list(
                coordinate=coordinate, delta_x=delta_x, delta_y=delta_y
            )
        elif xpath is not None:
            commands = self.__wxms_page_instance.simple_scroll_by_xpath_cdp_list(
                xpath=xpath, delta_x=delta_x, delta_y=delta_y
            )
        else:
            commands = self.__wxms_page_instance.simple_scroll_cdp_list(
                coordinate=(
                    self.__wxms_page_instance.resized_size[0] // 2,
                    self.__wxms_page_instance.resized_size[1] // 2,
                ),
                delta_x=delta_x,
                delta_y=delta_y,
            )
        self.__cdp_proxy(commands)
        return self.__process_standard_result(
            screenshot_file_name=self.__get_screenshot_file_name(
                self.standard_scroll.__name__
            ),
            config=config,
        )

    @context_util.add_trace_span(record_input=True)
    @clickhouse_service.add_action_decorator()
    def standard_wait(
        self, *, seconds: float, config: SandboxActionStandardConfig | None = None
    ) -> SandboxActionStandardOutput:
        """
        标准的等待操作

        :param seconds: 等待时间，单位秒
        :return: 标准操作的输出
        """
        if config is None:
            config = SandboxActionStandardConfig()
        self.__send_action_message_list(
            action_message_list=config.action_message_list_before_action
        )
        if seconds > 0:
            self.__sleep(seconds, force=True)
        return self.__process_standard_result(
            screenshot_file_name=self.__get_screenshot_file_name(
                self.standard_wait.__name__
            ),
            config=config,
        )

    def __cdp_proxy(self, data_list: list[dict]) -> list[dict]:
        res = []
        if self.__wxms_instance.headless_mode:
            res = self.__cdp_proxy_headless(data_list)
        else:
            for data in data_list:
                # TODO 后续 http 的也要支持多个 cdp 指令的调用下发
                if data.get("method") == "XWeb.Sleep":
                    sleep_ms = data["params"]["sleep_ms"]
                    time.sleep(sleep_ms / 1000)
                    res.append({})
                else:
                    res.append(self.__cdp_proxy_http(data))
        return res

    def __cdp_proxy_headless(self, data_list: list[dict]) -> list[dict]:
        if not data_list:
            return []
        return sandbox_util_service.base_cdp_proxy_headless(
            link_type=self.__wxms_instance.link_type,
            from_uin=1111,
            to_uin=self.__wxms_instance.uin,
            from_username=self.__wxms_instance.from_username,
            to_username=self.__wxms_instance.username,
            request_id=self.__wxms_instance.request_id,
            data=data_list,
        )

    def __cdp_proxy_http(self, data: dict) -> dict:
        response = httpx_util.send_request(
            method="POST",
            url=f"{self.__wxms_instance.base_url}/v1/sandbox/cdp_proxy/{self.__wxms_instance.uin}",
            json=data,
            timeout=10,
            span_name=data.get("method", None),
        )
        logger.debug(
            "get cdp proxy http response",
            extra={
                "customized_data_info": {
                    "text": response.text,
                    "status_code": response.status_code,
                }
            },
        )
        return response.json()

    @context_util.add_trace_span(record_output=True)
    @clickhouse_service.add_action_decorator()
    def __check_screenshot_loading_status(
        self,
        config: SandboxActionStandardConfig,
        output: SandboxActionStandardOutput,
    ) -> SandboxActionStandardOutput:
        """
        检查截图的加载状态，支持重试机制
        如果截出来的图片是一张表示页面处于加载中状态的图片，则尝试重新获取标准操作的结果并更新 output 的字段
        异步模式下通过信号队列通知返回检查结果
        """
        try:
            # 分两轮检查截图的加载状态
            for retry_count in range(
                self.__check_screenshot_loading_status_max_retry_count
                + self.__check_screenshot_loading_status_max_retry_count_for_back
            ):
                is_last_retry = (
                    retry_count
                    == self.__check_screenshot_loading_status_max_retry_count
                )
                if image_service.check_screenshot_loading_status(output, is_last_retry):
                    # 截出来的图片表示页面已经加载完成了
                    break
                if is_last_retry:
                    # 如果上一轮检查的最后一张图还是处于加载状态，则进行回退的逻辑
                    if self.__xweb_navigate_back(1):
                        self.__sleep(self.__sleep_seconds_before_screenshot, force=True)
                    else:
                        # 回退有可能会失败，例如启动阶段就回退
                        break
                # 生成重试截图的保存路径
                next_screenshot_file_path = (
                    output.screenshot_dto.file.rsplit(".", maxsplit=1)[0].rsplit(
                        "_loading_retry_", maxsplit=1
                    )[0]
                    + f"_loading_retry_{retry_count}.jpg"
                )
                image_service.check_screenshot_file(next_screenshot_file_path)
                new_output = self.__get_standard_result(
                    screenshot_file_path=next_screenshot_file_path,
                    config=config,
                )
                # 更新输出对象中的数据要在发送状态信号之前，所以记录下老的截图路径
                old_screenshot_file_path = output.screenshot_dto.file
                # 更新输出对象中的数据
                output.applet_page_info = new_output.applet_page_info
                output.dom_xml = new_output.dom_xml
                output.elements_rects = new_output.elements_rects
                output.native_elements_rects = new_output.native_elements_rects
                output.screenshot_dto = new_output.screenshot_dto
                # 异步模式下发送状态信号
                if self.__is_async_action_result:
                    self.__image_loading_status_signal_queue.put(
                        (old_screenshot_file_path, next_screenshot_file_path)
                    )
            # 异步模式下发送状态信号
            if self.__is_async_action_result:
                self.__image_loading_status_signal_queue.put(
                    (output.screenshot_dto.file, "")
                )
            # 发送动作完成后的 action message
            self.__send_action_message_list(
                action_message_list=config.action_message_list_after_action
            )
        except Exception as e:  # pylint: disable=W0718
            logger.error(
                "__check_screenshot_loading_status failed",
                extra={
                    "customized_data_info": {
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
            if self.__is_async_action_result:
                self.__image_loading_status_signal_queue.put(("error", str(e)))
            raise e
        finally:
            if self.__is_async_action_result:
                # 异步模式下发送最终状态信号，最终状态信号被获取后会标记操作处理完成
                self.__image_loading_status_signal_queue.put(("final", ""))
            else:
                # 同步模式下标记操作处理完成
                self.__processing_action_result = False
        return output

    def __get_screenshot_file_name(self, action: str) -> str:
        return f"{self.standard_action_count}_{action}.jpg"

    def __get_standard_result(
        self,
        screenshot_file_path: str,
        config: SandboxActionStandardConfig,
    ) -> SandboxActionStandardOutput:
        """
        执行截图及相关 cdp 指令，拿到指令返回后进行解析并封装成标准操作的输出

        具体步骤：
        1. 根据配置构造 cdp 指令列表，包括截图、当前页面信息、dom xml、所有元素的框矩形信息
        2. 执行指令，获取响应列表
        3. 因为截图的指令在第一个，所以先解析截图指令的响应，生成截图 url，并将上传图片任务加入队列进行异步上传
        4. 解析剩余 cdp 指令的响应
        5. 封装成标准操作的输出
        """
        commands = []
        commands.extend(self.__wxms_page_instance.simple_screenshot_cdp_list())
        commands.extend(
            self.__wxms_instance.xweb_get_applet_page_info_cdp_list(app_id=self.app_id)
        )
        if not config.dom_xml_disabled:
            commands.extend(
                self.__wxms_page_instance.get_dom_xml_cdp_list(
                    include_offscreen_elements=config.dom_xml_include_offscreen_elements
                )
            )
        if not config.elements_rects_disabled:
            commands.extend(
                self.__wxms_page_instance.get_all_elements_rects_with_offset_cdp_list()
            )
        cdp_response_list = self.__cdp_proxy(commands)
        self.__wxms_page_instance.simple_screenshot_parse_result(
            cdp_response_list=cdp_response_list[0:1],
            save_path=screenshot_file_path,
        )
        screenshot_url = image_service.get_image_url(
            object_key_dir=self.screenshot_object_key_dir, file=screenshot_file_path
        )
        # 将上传图片任务加入队列进行异步上传
        self.__upload_image_queue.put(
            (self.screenshot_object_key_dir, screenshot_file_path, screenshot_url)
        )
        screenshot_dto = ScreenshotDTO(
            file=screenshot_file_path,
            real_size=self.__wxms_page_instance.real_size,
            resized_size=self.__wxms_page_instance.resized_size,
            url=screenshot_url,
        )
        applet_page_info = cdp_response_list[1]
        idx = 2
        if config.dom_xml_disabled:
            dom_xml = {}
        else:
            dom_xml = self.__wxms_page_instance.get_dom_xml_parse_result(
                cdp_response_list[2:3]
            )
            idx += 1
        elements_rects, native_elements_rects = (
            ({}, {})
            if config.elements_rects_disabled
            else self.__wxms_page_instance.get_all_elements_rects_with_offset_parse_result(
                cdp_response_list[idx:]
            )
        )
        return SandboxActionStandardOutput(
            applet_page_info=applet_page_info,
            dom_xml=dom_xml,
            elements_rects=elements_rects,
            native_elements_rects=native_elements_rects,
            screenshot_dto=screenshot_dto,
        )

    def __process_standard_result(
        self, screenshot_file_name: str, config: SandboxActionStandardConfig
    ) -> SandboxActionStandardOutput:
        self.__processing_action_result = True
        # 保证操作结束页面开始变化后再截图
        self.__sleep(self.__sleep_seconds_before_screenshot, force=True)
        screenshot_file_path = f"{self.__screenshot_file_dir}/{screenshot_file_name}"
        image_service.check_screenshot_file(screenshot_file_path)
        res = self.__get_standard_result(
            screenshot_file_path=screenshot_file_path, config=config
        )
        # 是否异步判断截图的加载状态
        if self.__is_async_action_result:
            if not self.__image_loading_status_signal_queue.empty():
                # 如果发现不是空说明代码运行有严重错误，这里兜底做一下清除
                logger.error("self.__image_loading_status_signal_queue is not empty")
                self.get_image_loading_status_signal("final")
            self.__image_loading_status_signal_queue.put(("start", ""))
            self.__thread_pool.submit(
                self.__check_screenshot_loading_status,
                config=config,
                output=res,
            )
        else:
            self.__check_screenshot_loading_status(
                config=config,
                output=res,
            )
        self.standard_output_list.append(res)
        self.standard_action_count += 1
        return res

    def __send_action_message_list(
        self, action_message_list: list[SandboxActionMessage] | None
    ):
        if self.__wxms_instance.headless_mode:
            self.__thread_pool.submit(
                sandbox_util_service.send_action_message_list,
                link_type=self.__wxms_instance.link_type,
                from_uin=1111,
                to_uin=self.__wxms_instance.uin,
                from_username=self.__wxms_instance.from_username,
                to_username=self.__wxms_instance.username,
                request_id=self.__wxms_instance.request_id,
                app_id=self.app_id,
                action_message_list=action_message_list,
            )

    def __sleep(self, seconds: float, force: bool = False):
        """
        睡眠指定时间，无头的操作由于只在操作结束后才返回所以睡眠只需要万分之一

        :param seconds: 睡眠时间，单位秒
        :param force: 是否绕过无头的判断逻辑
        """
        if self.__wxms_instance.headless_mode and not force:
            time.sleep(seconds * 0.0001)
        else:
            time.sleep(seconds)

    @context_util.add_trace_span(record_input=True)
    @clickhouse_service.add_action_decorator()
    def __standard_start(
        self,
        *,
        config: SandboxActionStandardConfig | None,
        skip_launch_applet: bool,
    ) -> SandboxActionStandardOutput:
        """
        标准的开始操作
        """
        if config is None:
            config = SandboxActionStandardConfig()
        self.__send_action_message_list(
            action_message_list=config.action_message_list_before_action
        )
        self.__wxms_page_instance = self.__wxms_instance.simple_get_app(
            app_id=self.app_id,
            entry_url=self.app_entry_url,
            skip_launch_applet=skip_launch_applet,
        )
        self.target_id = self.__wxms_page_instance.target_id
        self.__cdp_proxy(self.__wxms_page_instance.dom_enable_cdp_list())
        return self.__process_standard_result(
            screenshot_file_name=self.__get_screenshot_file_name("standard_start"),
            config=config,
        )

    def __upload_image_worker(self):
        while not self.stop_flag or not self.__upload_image_queue.empty():
            try:
                object_key_dir, file, target_url = self.__upload_image_queue.get(
                    timeout=0.5
                )
                real_url = common_util.execute_function_with_retry(
                    image_service.upload_screenshot,
                    object_key_dir=object_key_dir,
                    file=file,
                )
                logger_func = logger.info if target_url == real_url else logger.error
                logger_func(
                    "upload image success",
                    extra={
                        "customized_data_info": {
                            "real_url": real_url,
                            "target_url": target_url,
                        }
                    },
                )
            except queue.Empty:
                pass
            except Exception as e:  # pylint: disable=W0718
                logger.warning(
                    "__upload_image_worker failed",
                    extra={
                        "customized_data_info": {
                            "exception": str(e),
                            "traceback": traceback.format_exc(),
                        }
                    },
                )
        logger.info("__upload_image_worker finished")

    def __xweb_navigate_back(self, back_num: int) -> bool:
        res = back_num > 0 and len(self.standard_output_list) > 0
        if res:
            standard_output_index = len(self.standard_output_list) - 1 - back_num
            if standard_output_index < 0:
                standard_output_index = 0
            target_url = (
                self.standard_output_list[standard_output_index]
                .applet_page_info.get("data", {})
                .get("data", {})
                .get("full_path", "")
            )
            self.__wxms_instance.xweb_navigate_back(
                back_num=back_num, target_url=target_url
            )
        else:
            logger.error(
                "__xweb_navigate_back invalid",
                extra={
                    "customized_data_info": {
                        "back_num": back_num,
                    }
                },
            )
        return res
