"""
沙箱工具类
"""

import json
import traceback

from wxms.logger import logger
from wxms.model.exception.error_code_exception import (
    APPLET_INTERRUPT_ERROR_CODE_DICT,
    AppletInterruptError,
)
from wxms.model.service.sandbox_service import (
    SandboxActionMessage,
)
from wxms.util.common_util import common_util
from wxms.util.httpx_util import httpx_util
from wxms.util.polaris_util import polaris_util
from wxms.util.time_util import time_util


class SandboxUtilService:
    """
    沙箱工具类
    """

    def __init__(self):
        pass

    def base_cdp_proxy_headless(
        self,
        link_type: str,
        from_uin: int,
        to_uin: int,
        from_username: str,
        to_username: str,
        request_id: str,
        data: list[dict],
    ) -> list[dict]:
        """
        下发无头小程序的指令并获取操作结果
        """
        request_data = {
            "link_type": link_type,
            "from_uin": from_uin,
            "to_uin": to_uin,
            "from_username": from_username,
            "to_username": to_username,
            "request_id": request_id,
            "cmd_id": common_util.get_random_id(32),
            "data": data,
        }
        try:
            logger.debug(
                "send cdp command debug before",
                extra={
                    "customized_data_info": {
                        "request_data": request_data,
                    }
                },
            )
            result = self.__send_cdp_command(**request_data)
            logger.debug(
                "send cdp command debug after __send_cdp_command",
                extra={
                    "customized_data_info": {
                        "request_data": request_data,
                        "result": result,
                    }
                },
            )
            return self.__base_cdp_proxy_headless_process_result(data, result)
        except AppletInterruptError as e:
            raise e
        except Exception as e:  # pylint: disable=W0718
            logger.error(
                "send cdp command failed",
                extra={
                    "customized_data_info": {
                        "request_data": request_data,
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
            return [
                {"code": -1, "message": f"cdp_proxy_headless err, {e}"} for _ in data
            ]

    def send_action_message_list(
        self,
        link_type: str,
        from_uin: int,
        to_uin: int,
        from_username: str,
        to_username: str,
        request_id: str,
        app_id: str,
        action_message_list: list[SandboxActionMessage] | None,
    ):
        """
        下发沙箱操作消息

        :param action_message_list: 沙箱操作消息的列表
        """
        if not action_message_list:
            return
        mode = 0  # 旁路消息
        if link_type == "rudp":
            mode = 1
        request_data = {
            "mode": mode,
            "from_uin": from_uin,
            "to_uin": to_uin,
            "from_username": from_username,
            "to_username": to_username,
            "data": {
                "session_id": request_id,
                "app_id": app_id,
                "timestamp": time_util.get_millisecond_timestamp_of_current_time()
                // 1000,
                "action_message_list": [i.model_dump() for i in action_message_list],
            },
        }
        try:
            response = httpx_util.send_request(
                method="POST",
                url=f"{self.__get_svrkithelper_address()}/api/v1/action_message",
                json=request_data,
                timeout=11,
                span_name="SendActionMessage",
            )
            res = response.json()
            logger.debug(
                "send action message debug after",
                extra={
                    "customized_data_info": {
                        "request_data": request_data,
                        "result": res,
                    }
                },
            )
        except Exception as e:  # pylint: disable=W0718
            logger.error(
                "send action message failed",
                extra={
                    "customized_data_info": {
                        "request_data": request_data,
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )

    def translate_coordinate_from_fixed_to_real(
        self,
        coordinate_fixed: tuple[int, int],
        real_size: tuple[int, int],
        resized_size: tuple[int, int],
    ) -> tuple[int, int]:
        """
        截图的时候会将设备的屏幕尺寸固定缩放至 resized_size 的框中
        从而导致视觉模型推理结果的坐标不是设备上的真实坐标
        所以如果是固定尺寸坐标系上的坐标，则需要转换成设备上的真实坐标

        :param coordinate_fixed: 固定尺寸坐标系上的坐标
        :param real_size: 设备上的真实宽高
        :param resized_size: resize 后的宽高（不含 padding 的留白）
        :return: 设备上的真实坐标
        """
        res_x = int(coordinate_fixed[0] * real_size[0] / resized_size[0])
        res_y = int(coordinate_fixed[1] * real_size[1] / resized_size[1])
        # 检查坐标是否在有效区域内
        if res_x < 0:
            res_x = 0
        if res_x > real_size[0]:
            res_x = real_size[0]
        if res_y < 0:
            res_y = 0
        if res_y > real_size[1]:
            res_y = real_size[1]
        return (res_x, res_y)

    def translate_coordinate_from_real_to_fixed(
        self,
        coordinate_real: tuple[int, int],
        real_size: tuple[int, int],
        resized_size: tuple[int, int],
    ) -> tuple[int, int]:
        """
        将设备上的真实坐标转换成固定尺寸坐标系上的坐标

        :param coordinate_real: 设备上的真实坐标
        :param real_size: 设备上的真实宽高
        :param resized_size: resize 后的宽高（不含 padding 的留白）
        :return: 固定尺寸坐标系上的坐标
        """
        res_x = int(coordinate_real[0] * resized_size[0] / real_size[0])
        res_y = int(coordinate_real[1] * resized_size[1] / real_size[1])
        # 检查坐标是否在有效区域内
        if res_x < 0:
            res_x = 0
        if res_x > resized_size[0]:
            res_x = resized_size[0]
        if res_y < 0:
            res_y = 0
        if res_y > resized_size[1]:
            res_y = resized_size[1]
        return (res_x, res_y)

    def translate_rect_from_real_to_fixed(
        self,
        coordinate_real: tuple[int, int],
        width_real: int,
        height_real: int,
        real_size: tuple[int, int],
        resized_size: tuple[int, int],
    ) -> tuple[tuple[int, int], int, int]:
        """
        将设备上的真实坐标框转换成固定尺寸坐标系上的坐标框

        :param coordinate_real: 设备上的真实坐标框的顶点，有可能不在有效区域内
        :param width_real: 设备上的真实坐标框的宽
        :param height_real: 设备上的真实坐标框的高
        :param real_size: 设备上的真实宽高
        :param resized_size: resize 后的宽高（不含 padding 的留白）
        :return: 固定尺寸坐标系上的坐标框的顶点，宽，高
        """
        res_x = int(coordinate_real[0] * resized_size[0] / real_size[0])
        res_y = int(coordinate_real[1] * resized_size[1] / real_size[1])
        res_width = int(width_real * resized_size[0] / real_size[0])
        res_height = int(height_real * resized_size[1] / real_size[1])
        return (res_x, res_y), res_width, res_height

    def __base_cdp_proxy_headless_process_result(
        self, raw_request_data_list: list[dict], result: dict
    ) -> list[dict]:
        """
        处理无头小程序的指令操作结果
        """
        if not result:
            return [
                {"code": -100, "message": "result empty"}
                for _ in range(len(raw_request_data_list))
            ]
        result_code = result.get("code", 0)
        if result_code != 0:
            return [
                {
                    "code": result_code,
                    "message": f"result_code err, {result}",
                }
                for _ in range(len(raw_request_data_list))
            ]
        result_cdp_error = result.get("cdp_error", 0)
        if result_cdp_error != 0:
            return [
                {
                    "code": result_cdp_error,
                    "message": f"result_cdp_error err, {result}",
                }
                for _ in range(len(raw_request_data_list))
            ]
        # cdp_response 的长度和下发的 cdp 指令的长度一致，但是顺序不是一致的，需要用 task_id 重新对齐
        cdp_response_list = result.get("cdp_response", [])
        if len(cdp_response_list) != len(raw_request_data_list):
            return [
                {
                    "code": -100,
                    "message": f"cdp_response_list length {len(cdp_response_list)} is not equal to {len(raw_request_data_list)}",  # pylint: disable=C0301
                }
                for _ in range(len(raw_request_data_list))
            ]
        temp_res = []
        for cdp_response_str in cdp_response_list:
            cdp_response = json.loads(cdp_response_str)
            cdp_response_code = cdp_response.get("code", 0)
            if cdp_response_code in APPLET_INTERRUPT_ERROR_CODE_DICT:
                logger.error(
                    "cdp_response interruped",
                    extra={
                        "customized_data_info": {
                            "cdp_response_list": cdp_response_list,
                        }
                    },
                )
                err_params = cdp_response.get("data", {}).get("data", {})
                err_params["error_code"] = cdp_response_code
                raise AppletInterruptError(
                    cdp_response_code,
                    err_params,
                )
            temp_res.append(cdp_response)
        # 使用 task_id 对齐顺序
        res = []
        for index, raw_request_data in enumerate(raw_request_data_list):
            request_task_id = raw_request_data.get("params", {}).get("task_id", "")
            for temp_res_index, cdp_response in enumerate(temp_res[index:]):
                response_task_id = (
                    cdp_response.get("data", {}).get("data", {}).get("task_id", "")
                )
                if request_task_id == response_task_id:
                    temp_res[index + temp_res_index] = temp_res[index]
                    temp_res[index] = cdp_response
                    res.append(cdp_response)
                    break
            if len(res) != index + 1:
                raise ValueError(
                    f"find task_id {request_task_id} in cdp_response_list failed"
                )
        return res

    def __get_svrkithelper_address(self) -> str:
        default_value = "mmfinderdrsandboxsvrkithelper.production.polaris:19602"
        address = polaris_util.get_service_address(
            service="mmfinderdrsandboxsvrkithelper",
            namespace="Production",
            default_value=default_value,
        )
        return f"http://{address}"

    def __send_cdp_command(
        self,
        link_type: str,
        from_uin: int,
        to_uin: int,
        from_username: str,
        to_username: str,
        request_id: str,
        cmd_id: str,
        data: list[dict],
    ) -> dict:
        span_name = ",".join([item.get("method", "") for item in data])
        mode = 0  # 旁路消息
        if link_type == "rudp":
            mode = 1
        response = httpx_util.send_request(
            method="POST",
            url=f"{self.__get_svrkithelper_address()}/v1/sandbox/cdp_proxy/{to_uin}",
            json={
                "mode": mode,
                "from_uin": from_uin,
                "to_uin": to_uin,
                "from_username": from_username,
                "to_username": to_username,
                "request_id": request_id,
                "cmd_id": cmd_id,
                "data": [json.dumps(item, ensure_ascii=False) for item in data],
                "timeout_ms": 10000,
                "message_type": 1,
            },
            timeout=11,
            span_name=span_name,
        )
        return response.json()


sandbox_util_service = SandboxUtilService()
