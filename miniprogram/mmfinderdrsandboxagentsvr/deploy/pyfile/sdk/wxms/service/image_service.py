"""
image 服务类
"""

import io
import json
import os
import re
import time
import traceback
from urllib.parse import urlparse

import cv2
import numpy as np
from cffi import FFI
from PIL import Image, ImageDraw, ImageFile, ImageFilter, ImageOps
from wxms.config.config import LLM_CONFIG, MIDDLEWARE_CONFIG, SCREENSHOT_CONFIG
from wxms.dao.redis_dao import redis_dao
from wxms.env import CONF_ENV, WXMS_FEATURE_ENABLE_NEW_RESIZE
from wxms.logger import logger
from wxms.middleware.cos import CosClient
from wxms.middleware.llm import LlmModel
from wxms.model.service.sandbox_service import (
    APPLET_FIXED_HEIGHT,
    APPLET_FIXED_WIDTH,
    SandboxActionStandardOutput,
    ScreenshotDTO,
    TaskNamespace,
    WXAMInfoDTO,
)
from wxms.service.clickhouse_service import clickhouse_service
from wxms.service.sandbox_util_service import sandbox_util_service
from wxms.util.common_util import common_util
from wxms.util.context_util import Traced<PERSON><PERSON><PERSON><PERSON><PERSON>Executor, context_util
from wxms.util.httpx_util import httpx_util

WXAM_HEADER_DEF = """
typedef enum {
    EWxAMDecoder_None = 0,
  EWxAMDecoder_OutOfMemory = -201,
  EWxAMDecoder_InvalidData = -202,
  EWxAMDecoder_InvalidArgument = -203,
  EWxAMDecoder_NotEnoughData = -204, // not enough data to parse header
  EWxAMDecoder_UnsupportPicType =
      -205, // not support this picType, for example: bitsteam has no alpha, but
            // want APNG output
  EWxAMDecoder_BufTooShort =
      -206, // not enough data to store the decoded picture
  EWxAMDecoder_StrictBufTooShort =
      -207, // not enough data to store the decoded picture and quit immediately
  EWxAMDecoder_AtomTypeError = -208,
  EWxAMDecoder_UnsupportFrmAtomType = -209,
  EWxAMDecoder_UnsupportGenAtomType = -210,
  EWxAMDecoder_WrongRotation = -211,
  EWxAMDecoder_WrongPaletteSize = -212,
  EWxAMDecoder_WrongGridPara = -213,
  EWxAMDecoder_NotAllFrmsCanBeDec = -214,
  EWxAMDecoder_NotSupportWxAM2HEIC = -215,
  EWxAMDecoder_WrongColorRangePara = -216,
  EWxAMDecoder_WrongJPGQuality = -217,
  EWxAMDecoder_WrongOutFmt = -218,
  EWxAMDecoder_VCODEC3_INVALAD = -219,
  EWxAMDecoder_VCODEC3_NULLBUF = -220,
  EWxAMDecoder_VCODEC3_WRONGFMT = -221,
  EWxAMDecoder_VCODEC3_WRONGSIZE = -222,
  EWxAMDecoder_VCODEC3_DECFAIL = -223,
  EWxAMDecoder_VCODEC2_DECFAIL = -224,
  EWxAMDecoder_WrongAtomVer = -225,
  EWxAMDecoder_WrongHDRColorMode = -226,
  EWxAMDecoder_WrongColorSpace = -227,
  EWxAMDecoder_InternalError = -300,
  // user data err
  EWxAMDecoder_UserDataInternalError = -400,
} EWxAMDecoderErrorCode;

typedef enum {
    WxAMDecoderPicType_JPG = 0,
    WxAMDecoderPicType_PNG = 1,
    WxAMDecoderPicType_APNG = 2,
    WxAMDecoderPicType_GIF = 3,
    WxAMDecoderPicType_HEIF = 4,
} EWxAMDecoderPicType;

typedef struct {
    int i_UserDataType;
    unsigned int ui_UserDataLen;
    unsigned char *pUserDataBuf;
} SWxAMUserData;

typedef struct {
    EWxAMDecoderPicType picType;
    int i_isStrictBufMode;
    int i_compressLvl;
    SWxAMUserData stUserData;
} SWxAMDecBaseOption;

typedef struct {
  int nWidth;
  int nHeight;
  int nFrmCount; // how many frms in this seq; 1 means normal pic and >1 means
                 // animation
  int nHasAlpha; // whether this seq has Alpha; 0 means No and 1 means YES
  int nAlphaMethod; // currently, only 1 or 2 is valid
                    // 1 is for binary encoding, the alpha is neighther
                    // 0(background) nor 255(foreground) 2 is for 8 bit lossy
                    // alpha encoding
  int bProgressive; // 0 for normal static picture or animation picture
                    // 1 for progressive picture, note for progressive picture,
                    // no animation is performed.
  int nUniAnimationFlag; // whether this seq has uniformAnimation; 0 means No
                         // and 1 means YES
  int nAnimationTime; // if nUniformAnimation is YES, this is the time (10ms in
                      // ver.2 and 1ms in ver.0 & ver.1)
  int nLoopCount;

  int nDisposalFlag; // 返回2：wxam 是"兼容性模式 生成的 带P3标志位的srgb
                     // wxam"；返回3：wxam 是"最终模式 生成的 非srgb的wxam"
  int nDisposalMethod;
} SWxAMPicInfo;

int wxam_dec_wxam2pic_5(unsigned char *pWxamStream, int WxamStreamLen,
                        unsigned char *pPicStream, int *pPicLen,
                        SWxAMDecBaseOption decOption);
                        
int wxam_dec_getWXGFInfo_5(unsigned char *data, int len,
                           SWxAMPicInfo *pWxAMPicInfo);
"""


class ImageService:
    """
    image 服务类
    """

    def __init__(self):
        self.cos_client = CosClient(
            region=MIDDLEWARE_CONFIG.cos_config.region,
            service_domain=MIDDLEWARE_CONFIG.cos_config.service_domain,
            endpoint=MIDDLEWARE_CONFIG.cos_config.endpoint,
            bucket=MIDDLEWARE_CONFIG.cos_config.bucket,
            secret_id=MIDDLEWARE_CONFIG.cos_config.secret_id,
            secret_key=MIDDLEWARE_CONFIG.cos_config.secret_key,
        )
        self.llm_model_for_judging_loading_screenshot = LlmModel(
            base_url=LLM_CONFIG.wait_model_config.base_url,
            api_key=LLM_CONFIG.wait_model_config.api_key,
        )
        self.llm_model_name_for_judging_loading_screenshot = (
            LLM_CONFIG.wait_model_config.model_name
        )
        self.ffi = FFI()
        self.ffi.cdef(WXAM_HEADER_DEF)
        self.lib = self.ffi.dlopen(
            os.path.dirname(__file__) + "/../resources/lib/libWxHevcDecoder.so"
        )
        self.__thread_pool = TracedThreadPoolExecutor(max_workers=10)

    def call_omniparser(self, url: str, file: str, rects: list[dict]) -> dict:
        """
        调用 omniparser 服务接口

        :param url: 服务接口的 url
        :param file: 本地文件的路径
        :param rects: 元素的框矩形信息列表
        :return: omniparser 服务接口返回处理后的结果
        """
        image_base64 = common_util.encode_image(file)
        if not image_base64 or not rects:
            return {}
        # 构造请求体
        payload = {
            "image_base64": image_base64,
            "rects": rects,
            "save_outputs": False,
            "max_area_ratio": 0.5,
        }
        res = {}
        try:
            # 发送POST请求
            response = httpx_util.send_request(
                method="POST",
                url=url,
                data=json.dumps(payload),
                headers={"Content-Type": "application/json"},
                span_name="OmniParser",
            )
            # 检查响应状态，如果请求失败则抛出异常
            response.raise_for_status()
            # 记录响应结果
            response_data = response.json()
            response_result = response_data.get("result", [])
            logger.info(
                "get omniparser response result",
                extra={"customized_data_info": {"response_result": response_result}},
            )
            for d in response_data["result"]:
                xpath = "/" + d["xpath"]
                ocr = d["paddleocr"]
                caption = d["florence2"]
                res[xpath] = ocr if ocr else caption
        except Exception as e:  # pylint: disable=W0718
            logger.error(
                "call_omniparser failed",
                extra={
                    "customized_data_info": {
                        "api_url": url,
                        "file": file,
                        "rects": rects,
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
        return res

    def check_screenshot_file(self, file: str):
        """
        检查小程序截图的本地文件的路径是否合法

        :param file: 即将保存的本地文件的路径
        """
        if not file.endswith(".jpg"):
            raise ValueError(f"wrong file type {file}")

    def check_screenshot_loading_status(
        self, output: SandboxActionStandardOutput, is_last_retry: bool
    ) -> bool:
        """
        判断小程序的截图是否是一张 loading 中的图片

        :param output: 标准操作的输出
        :param is_last_retry: 是否是最后一次判断这张截图
        :return: 小程序的截图是否是一张 loading 中的图片，True 表示不是 loading 中的图片
        """
        file = output.screenshot_dto.file
        url = output.screenshot_dto.url
        app_id = (
            output.applet_page_info.get("data", {}).get("data", {}).get("app_id", "")
        )
        version = (
            output.applet_page_info.get("data", {}).get("data", {}).get("version", "")
        )
        full_path = (
            output.applet_page_info.get("data", {}).get("data", {}).get("full_path", "")
        )
        image_hash = common_util.encode_image_by_md5(file)
        hit_page_loaded_image_hash_cache = False
        edge_ratio = -1
        edge_ratio_res = None
        res = False
        exception_str = ""
        traceback_str = ""
        try:
            if app_id and redis_dao.page_loaded_image_hash_cache_get(
                app_id=app_id,
                version=version,
                full_path=full_path,
                image_hash=image_hash,
            ):
                # 优先计算截图哈希，判断是否命中缓存，命中则说明加载完成
                hit_page_loaded_image_hash_cache = True
                res = True
            else:
                if not is_last_retry and SCREENSHOT_CONFIG.edge_ratio_check_enable:
                    # 非最后一轮截图检查，业务开关开启时使用边缘检测法判断截图是否加载中
                    edge_ratio_res, edge_ratio = (
                        self.__check_screenshot_loading_status_by_edge_ratio(file)
                    )
                if edge_ratio_res is not None and not edge_ratio_res:
                    res = False
                else:
                    # 二分类模型判断截图加载状态
                    prompt = """图片展示的是一个微信小程序的页面截图，请判断该页面是否加载完全，如果页面已加载完全，请回复是，如果页面没有加载完全，请回答否。
注意：1、回复需要遵从格式<think>...</think> <answer>...</answer>,其中<think>标签中是判断的理由和推理过程，<answer>标签中是判断结果，即“是”或者“否”
2、页面出现大片空白区域，认为没有加载完全，回复否；但是页面有搜索框的搜索结果为空，或者订单页面订单查询结果为无时，认为是加载完全的，回复是
3、如果页面出现白屏，则没有加载完成，回复否
4、如果页面有[跳过]字样，说明是开屏广告，没有加载完成，回复否
5、如果页面有[正在]字样，说明正在加载，没有加载完成，回复否"""
                    image_content_list = [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{common_util.encode_image(file)}",
                                "customized_url_for_trace": url,
                            },
                        }
                    ]
                    messages = [
                        {
                            "role": "user",
                            "content": image_content_list
                            + [{"type": "text", "text": prompt}],
                        }
                    ]
                    content, _ = (
                        self.llm_model_for_judging_loading_screenshot.chat_completion(
                            model=self.llm_model_name_for_judging_loading_screenshot,
                            messages=messages,
                            temperature=0.0,
                            seed=2025,
                        )
                    )
                    final_answer = ""
                    answer_match = re.search(
                        r"<answer>(.*?)</answer>", content, re.DOTALL
                    )
                    if answer_match:
                        final_answer = answer_match.group(1).strip()
                    res = final_answer.strip() == "是"
                    # 模型判断截图加载完成，异步更新
                    if app_id and res:
                        self.__thread_pool.submit(
                            redis_dao.page_loaded_image_hash_cache_set,
                            app_id=app_id,
                            version=version,
                            full_path=full_path,
                            image_hash=image_hash,
                        )
        except Exception as e:  # pylint: disable=W0718
            exception_str = str(e)
            traceback_str = traceback.format_exc()
            logger.warning(
                "check_screenshot_loading_status failed",
                extra={
                    "customized_data_info": {
                        "exception": exception_str,
                        "traceback": traceback_str,
                    }
                },
            )
        clickhouse_service.add_event_check_screenshot_loading_status(
            file=file,
            url=url,
            app_id=app_id,
            version=version,
            full_path=full_path,
            image_hash=image_hash,
            hit_page_loaded_image_hash_cache=hit_page_loaded_image_hash_cache,
            edge_ratio=edge_ratio,
            edge_ratio_res=edge_ratio_res,
            res=res,
            exception_str=exception_str,
            traceback_str=traceback_str,
        )
        return res

    def concat_images_vertically(
        self, screenshot_dto_list: list[ScreenshotDTO]
    ) -> tuple[str, str]:
        """
        纵向拼接多张图片（支持透明通道）

        :param screenshot_dto_list: screenshot_dto 列表，当为空列表时返回的 url 为空
        :return: 所有图片纵向拼接后的 url，图片 hash 去重后纵向拼接后的 url
        """
        if not screenshot_dto_list:
            return "", ""
        first_screenshot_dto = screenshot_dto_list[0]
        if len(screenshot_dto_list) == 1:
            return first_screenshot_dto.url, first_screenshot_dto.url
        # 加载图片并计算 md5_hash 值
        image_md5_hash_list = [
            common_util.encode_image_by_md5(i.file) for i in screenshot_dto_list
        ]
        all_image_obj_list = [Image.open(i.file) for i in screenshot_dto_list]
        # 处理所有图片的纵向拼接
        long_image_obj = self.__concat_images_vertically(
            image_obj_list=all_image_obj_list
        )
        path_prefix = first_screenshot_dto.file.rsplit("/", maxsplit=1)[0]
        long_image_path = f"{path_prefix}/long_img_{context_util.get_span_id()}.jpg"
        temp_url = urlparse(first_screenshot_dto.url)
        object_key_dir = temp_url.path.rsplit("/", maxsplit=1)[0]
        long_image_obj.save(long_image_path, format="JPEG", quality=95, subsampling=0)
        long_image_url = self.upload_screenshot(
            object_key_dir=object_key_dir, file=long_image_path
        )
        # 处理图片 hash 去重后的纵向拼接
        temp_set = set()
        unique_image_obj_list = []
        for i, md5_hash in enumerate(image_md5_hash_list):
            if md5_hash in temp_set:
                continue
            temp_set.add(md5_hash)
            unique_image_obj_list.append(all_image_obj_list[i])
        long_unique_image_url = long_image_url
        if len(unique_image_obj_list) == 1:
            # 去重后只剩下一张图片
            long_unique_image_url = first_screenshot_dto.url
        elif len(unique_image_obj_list) != len(all_image_obj_list):
            # 数量不一致说明存在重复的图片
            long_unique_image_obj = self.__concat_images_vertically(
                image_obj_list=unique_image_obj_list
            )
            long_unique_image_path = (
                f"{path_prefix}/long_unique_img_{context_util.get_span_id()}.jpg"
            )
            long_unique_image_obj.save(
                long_unique_image_path, format="JPEG", quality=95, subsampling=0
            )
            long_unique_image_url = self.upload_screenshot(
                object_key_dir=object_key_dir, file=long_unique_image_path
            )
        return long_image_url, long_unique_image_url

    def get_image_file_dir(self, app_id: str, uin: int) -> str:
        """
        生成存储图片的绝对路径的文件夹

        :param app_id: 小程序的 id
        :param uin: 微信 uin
        :return: 存储图片的绝对路径的文件夹，不以 / 结尾
        """
        root_folder = f"{os.path.dirname(__file__)}/outputs"
        time_tag = time.strftime("%Y%m%d_%H%M%S_%f")[:-3]
        res = f"{root_folder}/{app_id}/{uin}/{time_tag}_{common_util.get_random_id(8)}"
        os.makedirs(res)
        return res

    def get_image_object_key_dir(
        self, namespace: TaskNamespace, app_id: str, uin: int, request_id: str
    ) -> str:
        """
        生成存储图片在对象存储上的 object key 文件夹
        为了保证相同的 app_id + uin + request_id 在不同的请求中使用相同的文件夹，这里会使用 redis 进行缓存

        :param namespace: 所属的命名空间
        :param app_id: 小程序的 id
        :param uin: 微信 uin
        :param request_id: SandboxClient 对象的唯一标识，通常是 trace id
        :return: 存储图片在对象存储上的 object key 文件夹，不以 / 结尾
        """
        time_tag = time.strftime("%Y%m%d_%H")
        res = (
            f"mmfinderdrsandboxagentsvr/{CONF_ENV}/{namespace.value}"
            + f"/{app_id}/{uin}/{time_tag}/{request_id}"
        )
        if not redis_dao.image_object_key_dir_set(
            app_id=app_id,
            uin=uin,
            request_id=request_id,
            value=res,
        ):
            res_in_redis = redis_dao.image_object_key_dir_get(
                app_id=app_id,
                uin=uin,
                request_id=request_id,
            )
            logger.info(
                "get_image_object_key_dir use cache in redis",
                extra={
                    "customized_data_info": {
                        "original_value": res,
                        "cache_value": res_in_redis,
                    }
                },
            )
            res = res_in_redis
        return res

    def get_image_url(self, object_key_dir: str, file: str) -> str:
        """
        生成存储图片在对象存储上的 url

        :param object_key_dir: 存储图片在对象存储上的 object key 文件夹，用于构成 object_key
        :param file: 本地文件的路径
        :return: 存储图片在对象存储上的 url
        """
        object_key = self.__get_image_object_key(
            object_key_dir=object_key_dir, file=file
        )
        return (
            f"http://{self.cos_client.bucket}.{self.cos_client.endpoint}/{object_key}"
        )

    def image_save(
        self,
        path: str,
        image_bytes: bytes,
        capsule_rect: None | dict[str, int],
    ) -> tuple[tuple[int, int], tuple[int, int]]:
        """
        保存图片，resize 的步骤如下（长边短边并不是指长度长，而是指相较于固定尺寸比例的长边短边）：
        一，图片长边对齐进行 resize，从而使得宽等于 APPLET_FIXED_WIDTH 或高等于 APPLET_FIXED_HEIGHT
        二，图片左上角顶点对齐
        三，短边 padding 留白

        :param path: 保存图片的路径
        :param image_bytes: 图片的字节数据
        :param capsule_rect: 胶囊按钮的框，这个框会被填充颜色或做高斯模糊处理（需要坐标系转换）
        :return: 原始图片对应的宽高，步骤一 resize 后的宽高
        """
        image = Image.open(io.BytesIO(image_bytes))
        # 原来图片的尺寸
        real_size = image.size
        if real_size[0] * APPLET_FIXED_HEIGHT >= APPLET_FIXED_WIDTH * real_size[1] or (
            not WXMS_FEATURE_ENABLE_NEW_RESIZE
        ):
            # 下方留白
            resized_size = (
                APPLET_FIXED_WIDTH,
                round(real_size[1] * APPLET_FIXED_WIDTH / real_size[0]),
            )
        else:
            # 右方留白
            resized_size = (
                round(real_size[0] * APPLET_FIXED_HEIGHT / real_size[1]),
                APPLET_FIXED_HEIGHT,
            )
        # 保存图片
        # resize 图片
        image = image.resize(
            resized_size,
            Image.Resampling.LANCZOS,
        )
        if WXMS_FEATURE_ENABLE_NEW_RESIZE:
            # 左上角顶点对齐，短边 padding 留白
            image = ImageOps.pad(
                image,
                (APPLET_FIXED_WIDTH, APPLET_FIXED_HEIGHT),
                method=Image.Resampling.LANCZOS,
                color="white",
                centering=(0, 0),
            )
        if capsule_rect is not None:
            rect_x = capsule_rect.get("x", 0)
            rect_y = capsule_rect.get("y", 0)
            rect_width = capsule_rect.get("width", 0)
            rect_height = capsule_rect.get("height", 0)
            (rect_x, rect_y), rect_width, rect_height = (
                sandbox_util_service.translate_rect_from_real_to_fixed(
                    (rect_x, rect_y),
                    rect_width,
                    rect_height,
                    real_size,
                    resized_size,
                )
            )
            if rect_x < 0:
                rect_width = rect_width + rect_x
                rect_x = 0
            if rect_y < 0:
                rect_height = rect_height + rect_y
                rect_y = 0
            if SCREENSHOT_CONFIG.capsule_rect_process_mode in ["black", "white"]:
                # 方案一 直接填充颜色
                draw = ImageDraw.Draw(image)
                draw.rectangle(
                    [(rect_x, rect_y), (rect_x + rect_width, rect_y + rect_height)],
                    fill=SCREENSHOT_CONFIG.capsule_rect_process_mode,
                )
            elif SCREENSHOT_CONFIG.capsule_rect_process_mode.startswith("gaussian"):
                # 方案二 应用高斯模糊
                radius = 5
                radius_str = SCREENSHOT_CONFIG.capsule_rect_process_mode.split(" ")[1]
                if radius_str:
                    radius = int(radius_str)
                region = image.crop(
                    (rect_x, rect_y, rect_x + rect_width, rect_y + rect_height)
                )
                blurred = region.filter(ImageFilter.GaussianBlur(radius=radius))
                image.paste(blurred, (rect_x, rect_y))
        if image.mode == "RGBA":
            # 如果图像是 RGBA 模式，转换为 RGB 后再保存为 JPEG
            image = image.convert("RGB")
        image.save(path, format="JPEG", quality=95, subsampling=0)
        logger.info(
            "save image success",
            extra={
                "customized_data_info": {
                    "path": path,
                }
            },
        )
        return real_size, resized_size

    def mark_red_point_on_image(
        self,
        file: str,
        coordinate: tuple[int, int],
        output_path: str | None = None,
        size: int = 5,
    ):
        """
        在图片上标记一个红色的点

        :param file: 本地文件的路径
        :param coordinate: 红色点的坐标 (x, y)
        :param output_path: 输出图片路径，如果为 None 则覆盖原图片
        :param size: 红色点的大小
        """
        # 打开图片
        img = Image.open(file)
        # 如果图像是 RGBA 模式，转换为 RGB
        if img.mode == "RGBA":
            img = img.convert("RGB")
        # 创建一个可以在图片上绘制的对象
        draw = ImageDraw.Draw(img)
        # 计算标记的边界框（以给定点为中心的正方形）
        x, y = coordinate
        bbox = [
            (x - size, y - size),  # 左上角
            (x + size, y + size),  # 右下角
        ]
        # 绘制红色矩形（可以改为圆形或其他形状）
        draw.ellipse(bbox, fill="red", outline="red")
        # 保存结果
        img.save(output_path if output_path else file)

    def upload_screenshot(self, object_key_dir: str, file: str) -> str:
        """
        上传本地文件到 cos

        :param object_key_dir: 存储图片在对象存储上的 object key 文件夹，用于构成 object_key
        :param file: 本地文件的路径
        :return: object 的 download url
        """
        self.check_screenshot_file(file)
        object_key = self.__get_image_object_key(
            object_key_dir=object_key_dir, file=file
        )
        return self.cos_client.upload_object(
            object_key=object_key, file=file, content_type="image/jpeg"
        )

    def wxam_convert_to_pic(
        self,
        wxam_data: bytes,
        output_type: int,
        buf_size: int,
    ) -> bytes:
        """
        将 wxam 格式的二进制字节流转换成指定图片格式的二进制字节流

        :param wxam_data: wxam 格式的二进制字节流
        :param output_type: 图片格式，详见 EWxAMDecoderPicType
        :param buf_size: 缓冲区大小用于输出二进制字节流，通常是图片大小的两倍
        :return: 指定图片格式的二进制字节流
        """
        dec_option = self.ffi.new(
            "SWxAMDecBaseOption *",
            {
                "picType": output_type,
                "i_isStrictBufMode": 0,
                "i_compressLvl": 0,
                "stUserData": {
                    "i_UserDataType": 0,
                    "ui_UserDataLen": 0,
                    "pUserDataBuf": self.ffi.NULL,
                },
            },
        )
        buf_size = buf_size if buf_size else len(wxam_data) * 2
        pic_len = self.ffi.new("int *", buf_size)  # 初始预估大小
        pic_buf = self.ffi.new("unsigned char[]", pic_len[0])
        wxam_ptr = self.ffi.from_buffer(wxam_data)
        result = self.lib.wxam_dec_wxam2pic_5(  # type: ignore
            wxam_ptr, len(wxam_data), pic_buf, pic_len, dec_option[0]
        )
        # 处理缓冲区不足
        if result == self.lib.EWxAMDecoder_BufTooShort:  # type: ignore
            pic_buf = self.ffi.new("unsigned char[]", pic_len[0])
            result = self.lib.wxam_dec_wxam2pic_5(  # type: ignore
                wxam_ptr, len(wxam_data), pic_buf, pic_len, dec_option[0]
            )
        if result != self.lib.EWxAMDecoder_None:  # type: ignore
            raise RuntimeError(f"convert_wxam_to_pic failed with error: {result}")
        return bytes(self.ffi.buffer(pic_buf, pic_len[0]))  # type: ignore

    def wxam_get_info(self, wxam_data: bytes) -> WXAMInfoDTO:
        """
        获取 wxam 格式的二进制字节流的信息

        :param wxam_data: wxam 格式的二进制字节流
        :return: 字节流的信息，字节流信息读取是否成功
        """
        info = self.ffi.new("SWxAMPicInfo *")
        wxam_ptr = self.ffi.from_buffer(wxam_data)
        result = self.lib.wxam_dec_getWXGFInfo_5(  # type: ignore
            wxam_ptr, len(wxam_data), info
        )
        if result != self.lib.EWxAMDecoder_None:  # type: ignore
            raise RuntimeError(f"wxam_get_info failed with error: {result}")
        return WXAMInfoDTO(
            frame_count=info.nFrmCount,  # type: ignore
            has_alpha=info.nHasAlpha,  # type: ignore
            height=info.nHeight,  # type: ignore
            loop_count=info.nLoopCount,  # type: ignore
            width=info.nWidth,  # type: ignore
        )

    def __check_screenshot_loading_status_by_edge_ratio(
        self, file: str
    ) -> tuple[bool, float]:
        """
        判断图片是否加载完成（基于边缘像素占比）。
        边缘像素占比高于阈值，认为已加载完成；否则认为未加载完成；判断异常时也会认为是未加载完成。

        :param file: 本地文件的路径
        :return: 判断结果
            第一个元素：True 表示不是 loading 中的图片，False 表示还是 loading 中的图片或 edge_ratio 判断异常
            第二个元素：边缘像素占比
        """
        res = False
        edge_ratio = -1
        # 读取灰度图
        img = cv2.imread(file, cv2.IMREAD_GRAYSCALE)  # type: ignore # pylint: disable=E1101
        if img is None:
            return res, edge_ratio
        # Canny 边缘检测
        edges = cv2.Canny(img, 100, 200)  # type: ignore # pylint: disable=E1101
        # 计算边缘像素占比
        edge_ratio = np.count_nonzero(edges) / edges.size
        res = edge_ratio >= SCREENSHOT_CONFIG.edge_ratio_check_threshold
        return res, edge_ratio

    def __concat_images_vertically(
        self, image_obj_list: list[ImageFile.ImageFile]
    ) -> Image.Image:
        total_height = sum(i.height for i in image_obj_list)
        max_width = max(i.width for i in image_obj_list)
        # 创建新画布，并填充白色背景（RGBA 支持透明度）
        res_image_obj = Image.new("RGBA", (max_width, total_height))
        draw = ImageDraw.Draw(res_image_obj)
        draw.rectangle([(0, 0), (max_width, total_height)], fill="white")
        # 逐个拼接图片
        y_offset = 0
        for image_object in image_obj_list:
            # 统一转换为 RGBA 模式以保持透明通道
            if image_object.mode != "RGBA":
                image_object = image_object.convert("RGBA")
            res_image_obj.paste(image_object, (0, y_offset), image_object)
            y_offset += image_object.height
        if res_image_obj.mode == "RGBA":
            # 如果图像是 RGBA 模式，转换为 RGB 后再保存为 JPEG
            res_image_obj = res_image_obj.convert("RGB")
        return res_image_obj

    def __get_image_object_key(self, object_key_dir: str, file: str) -> str:
        """
        生成存储图片在对象存储上的 object key

        :param object_key_dir: 存储图片在对象存储上的 object key 文件夹，用于构成 object_key
        :param file: 本地文件的路径
        :return: 存储图片在对象存储上的 object key
        """
        md5_hash = common_util.encode_image_by_md5(file)
        file_name = file.split("/")[-1]
        temp_list = file_name.split(".")
        return f"{object_key_dir}/{temp_list[0]}_{md5_hash}.{temp_list[1]}"


image_service = ImageService()

if __name__ == "__main__":
    import base64

    logger.info(
        image_service.image_save(
            path="/Users/<USER>/Desktop/cat1.jpg",
            image_bytes=base64.b64decode(
                common_util.encode_image("/Users/<USER>/Desktop/cat.jpg")
            ),
            capsule_rect={
                "x": 100,
                "y": 100,
                "width": 20,
                "height": 20,
            },
        )
    )
    logger.info(
        image_service.check_screenshot_loading_status(
            SandboxActionStandardOutput(
                applet_page_info={
                    "data": {
                        "data": {
                            "app_id": "wx3dcca19d0aa51755",
                            "version": "0.1.2",
                            "full_path": "/home/<USER>",
                        }
                    }
                },
                dom_xml={},
                elements_rects={},
                native_elements_rects={},
                screenshot_dto=ScreenshotDTO(
                    file="/Users/<USER>/Desktop/cat.jpg",
                    real_size=(-1, -1),
                    resized_size=(-1, -1),
                    url="https://mmfinderdrsandbox-1258344707.cos-internal.ap-shanghai.tencentcos.cn/mmfinderdrsandboxagentsvr/online/data_collection/wx56dbc76c7b340f86/2330286761/20250707_23/d6179af607694a75b8c2d416c7bcd6ca/1_standard_click_05182ab0afc5e56a1d2342fbe314b884.jpg",  # pylint: disable=C0301
                ),
            ),
            False,
        )
    )
    logger.info(
        image_service.upload_screenshot(
            image_service.get_image_object_key_dir(
                TaskNamespace.UNKNOWN, "test_appid", 0, "00000aaa0a0a0a0"
            ),
            "/Users/<USER>/Desktop/cat.jpg",
        )
    )
