"""
七彩石读取配置，支持多线程读取、支持热加载
"""

import os
import secrets
import time
import traceback
from threading import Lock
from typing import Any

import yaml
from rainbow_cpplib.rainbow_client import RainbowClient
from wxms.env import CONF_ENV
from wxms.logger import logger
from wxms.model.exception.internal_exception import ConfigError


class RainbowConfig:
    """
    七彩石读取配置，支持多线程读取、支持热加载
    """

    def __init__(
        self,
        data_id_list: list[str],
        app_id: str,
        user_id: str,
        secret_key: str,
        data_change_callback=None,
    ):
        cache_dir = f"{int(round(time.time() * 1000))}_{secrets.token_hex(8 // 2)}"
        os.makedirs(cache_dir)
        self.__rainbow_client = RainbowClient(
            {
                "connectStr": "65026305:65536",  # 访问地址
                "isUsingPolaris": True,  # 是否使用北极星
                "fileCachePath": f"./{cache_dir}/",  # 缓存文件目录
                "tokenConfig": {
                    "isOpenSign": True,
                    "app_id": app_id,
                    "user_id": user_id,
                    "secret_key": secret_key,
                },
            }
        )
        self.__group = "wxms"
        self.__env_name = "Default" if CONF_ENV == "online" else "Development"
        self.__data_id_list = data_id_list

        self.__lock = Lock()
        self.__data: dict[str, dict[str, Any]] = {}
        self.__reload_config()
        self.__data_change_callback = data_change_callback
        logger.info(
            "init rainbow config success",
            extra={
                "customized_data_info": {
                    "cache_dir": cache_dir,
                }
            },
        )

    def get_config_dict(self, data_id: str) -> dict[str, Any]:
        """
        获取指定的配置

        :param data_id: 指定配置的 key
        """
        return self.__data.get(data_id, {})

    def init_config_watcher(self):
        """
        启动配置热加载
        """

        def on_config_change(configs: dict):
            try:
                logger.info(
                    "update config start",
                    extra={
                        "customized_data_info": {
                            "configs": configs,
                        }
                    },
                )
                temp_data = {}
                if configs and "data" in configs:
                    for group_item in configs["data"]:
                        key_values = group_item.get("key_values", [])
                        for kv in key_values:
                            key = kv.get("key")
                            value = kv.get("value")
                            if key in self.__data:
                                temp_data[key] = yaml.safe_load(value)
                self.__lock.acquire()
                for k, v in temp_data.items():
                    self.__data[k] = v
                if self.__data_change_callback is not None:
                    self.__data_change_callback()
            except Exception as e:  # pylint: disable=W0718
                logger.error(
                    "update config failed",
                    extra={
                        "customized_data_info": {
                            "configs": configs,
                            "exception": str(e),
                            "traceback": traceback.format_exc(),
                        }
                    },
                )
            finally:
                self.__lock.release()

        self.__rainbow_client.add_listener(
            group=self.__group, env_name=self.__env_name, callback=on_config_change
        )

    def __reload_config(self):
        configs = self.__rainbow_client.get_configs(
            group=self.__group, env_name=self.__env_name
        )
        all_config_dict = {}
        if configs and "data" in configs:
            for group_item in configs["data"]:
                key_values = group_item.get("key_values", [])
                for kv in key_values:
                    key = kv.get("key")
                    value = kv.get("value")
                    if key is not None:
                        all_config_dict[key] = value
        temp_data = {}
        for data_id in self.__data_id_list:
            temp_value = all_config_dict.get(data_id, None)
            if temp_value is None:
                raise ConfigError(data_id)
            temp_data[data_id] = yaml.safe_load(temp_value)
        self.__lock.acquire()
        self.__data = temp_data
        self.__lock.release()
