"""
配置
"""

from pydantic import BaseModel
from wxms.middleware.rainbow_config import RainbowConfig


class LlmConfig(BaseModel):
    """
    大模型的配置
    """

    class LlmParameterConfig(BaseModel):
        """
        大模型参数的配置
        """

        api_key: str
        base_url: str
        model_name: str

    wait_model_config: LlmParameterConfig


class MiddlewareConfig(BaseModel):
    """
    中间件的配置
    """

    class CosConfig(BaseModel):
        """
        cos 的配置
        """

        bucket: str
        endpoint: str
        region: str
        secret_id: str
        secret_key: str
        service_domain: str

    class PulsarConfig(BaseModel):
        """
        pulsar 的配置
        """

        authentication_token: str
        service_url: str

    class RedisConfig(BaseModel):
        """
        redis 的配置
        """

        host: str
        password: str
        port: int

    cos_config: CosConfig
    pulsar_config: PulsarConfig
    redis_config: RedisConfig


class ScreenshotConfig(BaseModel):
    """
    截图的配置
    """

    capsule_rect_process_mode: str
    edge_ratio_check_enable: bool
    edge_ratio_check_threshold: float


class StandardInputConfig(BaseModel):
    """
    标准的输入操作的配置
    """

    sleep_before_input_dict: dict[str, int]


DATA_ID_LIST = [
    "LLM_CONFIG",
    "MIDDLEWARE_CONFIG",
    "SCREENSHOT_CONFIG",
    "STANDARD_INPUT_CONFIG",
]


def data_change_callback():
    """
    配置变更的回调函数
    """
    global_dict = globals()
    for data_id in DATA_ID_LIST:
        obj = global_dict.get(data_id)
        if obj and isinstance(obj, BaseModel):
            cls = obj.__class__
            new_obj = cls.model_validate(rainbow_config.get_config_dict(data_id))
            members = obj.model_fields.keys()
            for m in members:
                setattr(obj, m, getattr(new_obj, m))


rainbow_config = RainbowConfig(
    data_id_list=DATA_ID_LIST,
    app_id="v1_rb6_471a9f08-fe94-4229-b30c-93440",
    user_id="03f8ea9c92a446bd8e1ec6ccf821324f",
    secret_key="11f48c26431b71e0c50cd56c5ea9e7b6696a",
    data_change_callback=data_change_callback,
)

LLM_CONFIG = LlmConfig.model_validate(rainbow_config.get_config_dict("LLM_CONFIG"))
MIDDLEWARE_CONFIG = MiddlewareConfig.model_validate(
    rainbow_config.get_config_dict("MIDDLEWARE_CONFIG")
)
SCREENSHOT_CONFIG = ScreenshotConfig.model_validate(
    rainbow_config.get_config_dict("SCREENSHOT_CONFIG")
)
STANDARD_INPUT_CONFIG = StandardInputConfig.model_validate(
    rainbow_config.get_config_dict("STANDARD_INPUT_CONFIG")
)
