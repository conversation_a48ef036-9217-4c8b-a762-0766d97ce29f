"""
测试入口
"""

import os

from opentelemetry import trace
from opentelemetry.trace.propagation.tracecontext import TraceContextTextMapPropagator
from wxms.env import (
    WXMS_BASE_URL,
)
from wxms.logger import logger
from wxms.model.service.sandbox_service import TaskNamespace
from wxms.service.sandbox_service import SandboxClient
from wxms.util.context_util import context_util

WXMS_APP_ENTRY_URL = os.getenv("WXMS_APP_ENTRY_URL", "")
WXMS_APP_ID = os.getenv("WXMS_APP_ID", "")
WXMS_FROM_USERNAME = os.getenv("WXMS_FROM_USERNAME", "")
WXMS_HEADLESS_MODE = int(os.getenv("WXMS_HEADLESS_MODE", "0"))
WXMS_UIN = int(os.getenv("WXMS_UIN", "0"))
WXMS_USERNAME = os.getenv("WXMS_USERNAME", "")

tracer = trace.get_tracer(__name__)
span_context = tracer.start_as_current_span(
    "test",
    TraceContextTextMapPropagator().extract(
        {
            "traceparent": context_util.generate_traceparent(),
            "tracestate": "appid=mmfinderdrsandbox",
        }
    ),
)
span = span_context.__enter__()  # pylint: disable=C2801
trace_id = trace.format_trace_id(span.get_span_context().trace_id)
logger.info("start")
sandbox_client = SandboxClient(
    namespace=TaskNamespace.UNKNOWN,
    base_url=WXMS_BASE_URL,
    app_id=WXMS_APP_ID,
    app_entry_url=WXMS_APP_ENTRY_URL,
    uin=WXMS_UIN,
    from_username=WXMS_FROM_USERNAME,
    username=WXMS_USERNAME,
    headless_mode=WXMS_HEADLESS_MODE,
    standard_start_config=None,
    check_screenshot_loading_status_max_retry_count=3,
    check_screenshot_loading_status_max_retry_count_for_back=0,
    is_async_action_result=False,
    link_type="",
    skip_launch_applet=False,
    sleep_seconds_before_screenshot=0.2,
)
# 测试模型
# import time

# from wxms.middleware.llm import LlmModel
# from wxms.util.common_util import common_util

# llm_model = LlmModel(
#     base_url="http://drhttpsvr.polaris:8000/v1/llm-luban-waitmodel_7B_606_Qwen2.5-VL-7B-wait-0702_ck1100_export-0702-19",  # pylint: disable=C0301
#     api_key="EMPTY",
# )
# input_imgs = ["./0_standard_start.jpg"]
# input_imgs_content = [
#     {
#         "type": "image_url",
#         "image_url": {
#             "url": f"data:image/jpeg;base64,{common_util.encode_image(img)}",
#             "customized_url_for_trace": "http://mmfinderdrsandbox-1258344707.cos-internal.ap-shanghai.tencentcos.cn/mmfinderdrsandboxagentsvr/online/data_collection/wxc30ae3bc7fb4cab1/517709840/20250707_03/2450f74951af48238b2df32786860201/0_standard_start_d8abc702748d75c923e10094b187c646.jpg",  # pylint: disable=C0301
#         },
#     }
#     for img in input_imgs
# ]
# logger.info(
#     llm_model.chat_completion(
#         model="llm-luban-waitmodel_7B_606_Qwen2.5-VL-7B-wait-0702_ck1100_export-0702-19",
#         messages=[
#             {
#                 "role": "user",
#                 "content": input_imgs_content + [{"type": "text", "text": "hello"}],
#             },
#         ],
#     )
# )
# time.sleep(3)
logger.info("init success")
