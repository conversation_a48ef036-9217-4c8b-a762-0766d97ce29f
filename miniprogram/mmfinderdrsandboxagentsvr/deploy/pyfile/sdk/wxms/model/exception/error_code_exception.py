"""
error code 相关的 exception
"""

from enum import Enum

from pydantic import BaseModel


class ErrorCodeObj(BaseModel):
    """
    error code 的实体类
    """

    error_code: int
    error_message: str
    error_title: str


class ErrorCode(Enum):
    """
    error code 的枚举
    """

    APPLET_INTERRUPT_ERROR_NOT_IN_FOREGROUND = ErrorCodeObj(error_code=-1, error_message="小程序不在前台", error_title="Applet Interrupt Error")  # fmt: skip # pylint: disable=C0301
    APPLET_INTERRUPT_ERROR_NOT_IN_AGENT_MODE = ErrorCodeObj(error_code=-2, error_message="小程序不在 agent 模式（通过非 launchApplet 的形式启动过小程序）", error_title="Applet Interrupt Error")  # fmt: skip # pylint: disable=C0301
    APPLET_INTERRUPT_ERROR_COMMAND_NOT_BOUND = ErrorCodeObj(error_code=-3, error_message="命令未绑定到小程序", error_title="Applet Interrupt Error")  # fmt: skip # pylint: disable=C0301
    APPLET_INTERRUPT_ERROR_WEBVIEW_NOT_EXIST = ErrorCodeObj(error_code=-4, error_message="小程序 webview 不存在", error_title="Applet Interrupt Error")  # fmt: skip # pylint: disable=C0301
    # APPLET_INTERRUPT_ERROR_COMMAND_EXECUTION_ERROR = ErrorCodeObj(error_code=-5, error_message="命令运行错误", error_title="Applet Interrupt Error")  # fmt: skip # pylint: disable=C0301
    # APPLET_INTERRUPT_ERROR_COMMAND_PARAMETER_ERROR = ErrorCodeObj(error_code=-6, error_message="命令参数错误", error_title="Applet Interrupt Error")  # fmt: skip # pylint: disable=C0301
    APPLET_INTERRUPT_ERROR_WAIT_FOR_CASHIER  = ErrorCodeObj(error_code=-7, error_message="待拉起收银台，中断操作", error_title="Applet Interrupt Error")  # fmt: skip # pylint: disable=C0301
    APPLET_INTERRUPT_ERROR_NOT_SUPPORT_SKYLINE  = ErrorCodeObj(error_code=-8, error_message="不支持 skyline", error_title="Applet Interrupt Error")  # fmt: skip # pylint: disable=C0301
    APPLET_INTERRUPT_ERROR_USER_INTERRUPT  = ErrorCodeObj(error_code=-9, error_message="用户中断", error_title="Applet Interrupt Error")  # fmt: skip # pylint: disable=C0301
    APPLET_INTERRUPT_ERROR_UNKNOWN  = ErrorCodeObj(error_code=-100, error_message="Unknown", error_title="Applet Interrupt Error")  # fmt: skip # pylint: disable=C0301
    SUCCESS = ErrorCodeObj(error_code=0, error_message="Success", error_title="Success")  # fmt: skip # pylint: disable=C0301
    UNKNOWN = ErrorCodeObj(error_code=1, error_message="server error, please try again later", error_title="Unknown Error")  # fmt: skip # pylint: disable=C0301


APPLET_INTERRUPT_ERROR_CODE_DICT = {
    i.value.error_code: i
    for i in ErrorCode
    if i.value.error_title == "Applet Interrupt Error"
}


class BaseErrorCodeException(Exception):
    """
    error code 相关错误的基类
    """

    def __init__(self, error_code: ErrorCode, headers: dict[str, str] | None = None):
        super().__init__(error_code.value.error_message)
        self.error_message = error_code.value.error_message
        self.error_code = error_code
        self.headers = headers

    def __str__(self):
        return self.error_message


class AppletInterruptError(BaseErrorCodeException):
    """
    中断错误，调用方需要处理该错误用于中断推理过程
    """

    def __init__(self, code: int, params: dict):
        error_code = APPLET_INTERRUPT_ERROR_CODE_DICT.get(
            code, ErrorCode.APPLET_INTERRUPT_ERROR_UNKNOWN
        )
        super().__init__(error_code)
        self.params = params
