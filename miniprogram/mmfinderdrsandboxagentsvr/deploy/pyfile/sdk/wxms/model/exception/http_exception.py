"""
请按照 status_code 排序后再按照类名进行首字母排序
"""

from fastapi import HTTPException


class BaseHTTPException(HTTPException):
    """
    http 相关错误的基类
    """

    def __init__(
        self, status_code: int, detail: str, headers: dict[str, str] | None = None
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)
        self.error_message = detail

    def __str__(self):
        return self.error_message


class UnauthorizedBasicError(BaseHTTPException):
    """
    鉴权失败
    """

    def __init__(
        self,
        message: str = "Authentication Failed",
        headers: dict | None = None,
    ):
        headers = headers if headers is not None else {"WWW-Authenticate": "Basic"}
        super().__init__(status_code=401, detail=message, headers=headers)
