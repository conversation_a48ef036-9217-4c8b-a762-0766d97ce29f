"""
请按照类名首字母排序
"""


class BaseInternalException(Exception):
    """
    内部相关错误的基类
    """

    def __init__(self, error_message: str):
        super().__init__(error_message)
        self.error_message = error_message

    def __str__(self):
        return self.error_message


class ConfigError(BaseInternalException):
    """
    配置错误
    """

    def __init__(self, config_name: str):
        error_message = f"config {config_name} is wrong"
        super().__init__(error_message=error_message)


class DownloadFileError(BaseInternalException):
    """
    下载文件错误
    """

    def __init__(self, file: str):
        error_message = f"download file {file} error"
        super().__init__(error_message=error_message)


class UploadFileError(BaseInternalException):
    """
    上传文件错误
    """

    def __init__(self, file: str):
        error_message = f"upload file {file} error"
        super().__init__(error_message=error_message)
