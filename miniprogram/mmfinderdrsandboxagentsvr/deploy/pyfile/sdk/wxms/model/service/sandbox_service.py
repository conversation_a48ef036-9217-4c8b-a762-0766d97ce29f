"""
sandbox_service 相关的业务 model
"""

import uuid
from enum import IntEnum, StrEnum

from pydantic import BaseModel, Field

APPLET_FIXED_HEIGHT = 776
APPLET_FIXED_WIDTH = 410


class ScreenshotDTO(BaseModel):
    """
    截图的数据传输类
    """

    file: str
    real_size: tuple[int, int]
    resized_size: tuple[int, int]
    url: str


class SandboxActionMessageStyleType(IntEnum):
    """
    沙箱操作消息的样式类型
    """

    TITLE = 0
    SUBTITLE = 1


class SandboxActionMessage(BaseModel):
    """
    沙箱操作消息
    """

    content: str
    process_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    style_type: SandboxActionMessageStyleType


class SandboxActionStandardConfig(BaseModel):
    """
    沙箱操作的标准配置
    """

    action_message_list_after_action: list[SandboxActionMessage] | None = None
    action_message_list_before_action: list[SandboxActionMessage] | None = None
    dom_xml_disabled: bool = False
    dom_xml_include_offscreen_elements: bool = False
    elements_rects_disabled: bool = False


class SandboxActionStandardOutput(BaseModel):
    """
    沙箱操作的标准输出
    """

    applet_page_info: dict
    dom_xml: dict
    elements_rects: dict
    native_elements_rects: dict
    screenshot_dto: ScreenshotDTO


class TaskNamespace(StrEnum):
    """
    task 参数 namespace 的枚举
    """

    ANNOTATION = "annotation"
    DATA_COLLECTION = "data_collection"
    DIFY = "dify"
    EVALUATION = "evaluation"
    TRAINING = "training"
    UNKNOWN = "unknown"


class WXAMInfoDTO(BaseModel):
    """
    wxam 格式的二进制字节流的信息传输类
    """

    frame_count: int
    has_alpha: int
    height: int
    loop_count: int
    width: int
