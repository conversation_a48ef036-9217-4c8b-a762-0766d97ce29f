"""
通用的 api model
"""

from typing import Generic, TypeVar

from pydantic import BaseModel, Field
from wxms.model.exception.error_code_exception import ErrorCode

T = TypeVar("T")


class BaseHttpResponse(BaseModel, Generic[T]):
    """
    接口返回的基类
    """

    code: int = Field(..., title="业务相关的 code")
    message: str = Field(..., title="业务相关的信息")
    data: T = Field(..., title="数据")

    @classmethod
    def make_response(
        cls,
        code: int = ErrorCode.SUCCESS.value.error_code,
        message: str = ErrorCode.SUCCESS.value.error_message,
        data: T = None,
        **kwargs
    ):
        """
        构造对象
        """
        return cls(code=code, message=message, data=data, **kwargs)
