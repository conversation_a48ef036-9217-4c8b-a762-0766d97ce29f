
window.__WX_WM_SANDBOX_VERSION__ = '0.3.28';
window.__WX_WM_SANDBOX_BUILD_TIME__ = '2025/7/28 20:06:00';
"use strict";(()=>{var M={silent:Number.NEGATIVE_INFINITY,fatal:0,error:0,warn:1,log:2,info:3,success:3,fail:3,ready:3,start:3,box:3,debug:4,trace:5,verbose:Number.POSITIVE_INFINITY},se={silent:{level:-1},fatal:{level:M.fatal},error:{level:M.error},warn:{level:M.warn},log:{level:M.log},info:{level:M.info},success:{level:M.success},fail:{level:M.fail},ready:{level:M.info},start:{level:M.info},box:{level:M.info},debug:{level:M.debug},trace:{level:M.trace},verbose:{level:M.verbose}};function oe(e){if(e===null||typeof e!="object")return!1;let t=Object.getPrototypeOf(e);return t!==null&&t!==Object.prototype&&Object.getPrototypeOf(t)!==null||Symbol.iterator in e?!1:Symbol.toStringTag in e?Object.prototype.toString.call(e)==="[object Module]":!0}function ae(e,t,n=".",i){if(!oe(t))return ae(e,{},n,i);let r=Object.assign({},t);for(let c in e){if(c==="__proto__"||c==="constructor")continue;let d=e[c];d!=null&&(i&&i(r,c,d,n)||(Array.isArray(d)&&Array.isArray(r[c])?r[c]=[...d,...r[c]]:oe(d)&&oe(r[c])?r[c]=ae(d,r[c],(n?`${n}.`:"")+c.toString(),i):r[c]=d))}return r}function it(e){return(...t)=>t.reduce((n,i)=>ae(n,i,"",e),{})}var st=it();function at(e){return Object.prototype.toString.call(e)==="[object Object]"}function ct(e){return!(!at(e)||!e.message&&!e.args||e.stack)}var re=!1,Ie=[],x=class e{options;_lastLog;_mockFn;constructor(t={}){let n=t.types||se;this.options=st({...t,defaults:{...t.defaults},level:ie(t.level,n),reporters:[...t.reporters||[]]},{types:se,throttle:1e3,throttleMin:5,formatOptions:{date:!0,colors:!1,compact:!0}});for(let i in n){let r={type:i,...this.options.defaults,...n[i]};this[i]=this._wrapLogFn(r),this[i].raw=this._wrapLogFn(r,!0)}this.options.mockFn&&this.mockTypes(),this._lastLog={}}get level(){return this.options.level}set level(t){this.options.level=ie(t,this.options.types,this.options.level)}prompt(t,n){if(!this.options.prompt)throw new Error("prompt is not supported!");return this.options.prompt(t,n)}create(t){let n=new e({...this.options,...t});return this._mockFn&&n.mockTypes(this._mockFn),n}withDefaults(t){return this.create({...this.options,defaults:{...this.options.defaults,...t}})}withTag(t){return this.withDefaults({tag:this.options.defaults.tag?this.options.defaults.tag+":"+t:t})}addReporter(t){return this.options.reporters.push(t),this}removeReporter(t){if(t){let n=this.options.reporters.indexOf(t);if(n!==-1)return this.options.reporters.splice(n,1)}else this.options.reporters.splice(0);return this}setReporters(t){return this.options.reporters=Array.isArray(t)?t:[t],this}wrapAll(){this.wrapConsole(),this.wrapStd()}restoreAll(){this.restoreConsole(),this.restoreStd()}wrapConsole(){for(let t in this.options.types)console["__"+t]||(console["__"+t]=console[t]),console[t]=this[t].raw}restoreConsole(){for(let t in this.options.types)console["__"+t]&&(console[t]=console["__"+t],delete console["__"+t])}wrapStd(){this._wrapStream(this.options.stdout,"log"),this._wrapStream(this.options.stderr,"log")}_wrapStream(t,n){t&&(t.__write||(t.__write=t.write),t.write=i=>{this[n].raw(String(i).trim())})}restoreStd(){this._restoreStream(this.options.stdout),this._restoreStream(this.options.stderr)}_restoreStream(t){t&&t.__write&&(t.write=t.__write,delete t.__write)}pauseLogs(){re=!0}resumeLogs(){re=!1;let t=Ie.splice(0);for(let n of t)n[0]._logFn(n[1],n[2])}mockTypes(t){let n=t||this.options.mockFn;if(this._mockFn=n,typeof n=="function")for(let i in this.options.types)this[i]=n(i,this.options.types[i])||this[i],this[i].raw=this[i]}_wrapLogFn(t,n){return(...i)=>{if(re){Ie.push([this,t,i,n]);return}return this._logFn(t,i,n)}}_logFn(t,n,i){if((t.level||0)>this.level)return!1;let r={date:new Date,args:[],...t,level:ie(t.level,this.options.types)};!i&&n.length===1&&ct(n[0])?Object.assign(r,n[0]):r.args=[...n],r.message&&(r.args.unshift(r.message),delete r.message),r.additional&&(Array.isArray(r.additional)||(r.additional=r.additional.split(`
`)),r.args.push(`
`+r.additional.join(`
`)),delete r.additional),r.type=typeof r.type=="string"?r.type.toLowerCase():"log",r.tag=typeof r.tag=="string"?r.tag:"";let c=(u=!1)=>{let w=(this._lastLog.count||0)-this.options.throttleMin;if(this._lastLog.object&&w>0){let E=[...this._lastLog.object.args];w>1&&E.push(`(repeated ${w} times)`),this._log({...this._lastLog.object,args:E}),this._lastLog.count=1}u&&(this._lastLog.object=r,this._log(r))};clearTimeout(this._lastLog.timeout);let d=this._lastLog.time&&r.date?r.date.getTime()-this._lastLog.time.getTime():0;if(this._lastLog.time=r.date,d<this.options.throttle)try{let u=JSON.stringify([r.type,r.tag,r.args]),w=this._lastLog.serialized===u;if(this._lastLog.serialized=u,w&&(this._lastLog.count=(this._lastLog.count||0)+1,this._lastLog.count>this.options.throttleMin)){this._lastLog.timeout=setTimeout(c,this.options.throttle);return}}catch{}c(!0)}_log(t){for(let n of this.options.reporters)n.log(t,{options:this.options})}};function ie(e,t={},n=3){return e===void 0?n:typeof e=="number"?e:t[e]&&t[e].level!==void 0?t[e].level:n}x.prototype.add=x.prototype.addReporter;x.prototype.remove=x.prototype.removeReporter;x.prototype.clear=x.prototype.removeReporter;x.prototype.withScope=x.prototype.withTag;x.prototype.mock=x.prototype.mockTypes;x.prototype.pause=x.prototype.pauseLogs;x.prototype.resume=x.prototype.resumeLogs;function Ne(e={}){return new x(e)}var ce=class{options;defaultColor;levelColorMap;typeColorMap;constructor(t){this.options={...t},this.defaultColor="#7f8c8d",this.levelColorMap={0:"#c0392b",1:"#f39c12",3:"#00BCD4"},this.typeColorMap={success:"#2ecc71"}}_getLogFn(t){return t<1?console.__error||console.error:t===1?console.__warn||console.warn:console.__log||console.log}log(t){let n=this._getLogFn(t.level),i=t.type==="log"?"":t.type,r=t.tag||"",d=`
      background: ${this.typeColorMap[t.type]||this.levelColorMap[t.level]||this.defaultColor};
      border-radius: 0.5em;
      color: white;
      font-weight: bold;
      padding: 2px 0.5em;
    `,u=`%c${[r,i].filter(Boolean).join(":")}`;typeof t.args[0]=="string"?n(`${u}%c ${t.args[0]}`,d,"",...t.args.slice(1)):n(u,d,...t.args)}};function lt(e={}){return Ne({reporters:e.reporters||[new ce({})],prompt(n,i={}){return i.type==="confirm"?Promise.resolve(confirm(n)):Promise.resolve(prompt(n))},...e})}var X=lt();function Ce(){typeof window>"u"||(window.isPdfViewer=!!document?.body?.querySelector('body > embed[type="application/pdf"][width="100%"]'),window.isPdfViewer||(()=>{if(window._eventListenerTrackerInitialized)return;window._eventListenerTrackerInitialized=!0;let e=EventTarget.prototype.addEventListener,t=new WeakMap;EventTarget.prototype.addEventListener=function(n,i,r){if(typeof i=="function"){let c=t.get(this);c||(c=[],t.set(this,c)),c.push({type:n,listener:i,listenerPreview:i.toString().slice(0,100),options:r})}return r=typeof r=="object"?r:{capture:!!r},e.call(this,n,i,r)},window.getEventListenersForNode=n=>(t.get(n)||[]).map(({type:r,listenerPreview:c,options:d})=>({type:r,listenerPreview:c,options:d}))})())}var Le=new Set(["pointer","move","text","grab","grabbing","cell","copy","alias","all-scroll","col-resize","context-menu","crosshair","e-resize","ew-resize","help","n-resize","ne-resize","nesw-resize","ns-resize","nw-resize","nwse-resize","row-resize","s-resize","se-resize","sw-resize","vertical-text","w-resize","zoom-in","zoom-out"]),Se=new Set(["not-allowed","no-drop","wait","progress","initial","inherit"]),z=new Set(["a","button","input","select","textarea","details","summary","label","option","optgroup","fieldset","legend","wx-button","wx-input"]),Re=new Set(["disabled","readonly"]);function ke(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;let t=e.tagName.toLowerCase();return z.has(t)?!0:e.hasAttribute("onclick")||e.hasAttribute("role")||e.hasAttribute("tabindex")||e.hasAttribute("aria-")||e.hasAttribute("data-action")||e.getAttribute("contenteditable")==="true"}var le=new Set(["button","menuitemradio","menuitemcheckbox","radio","checkbox","tab","switch","slider","spinbutton","combobox","searchbox","textbox","option","scrollbar"]);var ue=(typeof window<"u"?window.__WX_MER__?.consoleLog:void 0)||console.log,ut=(typeof window<"u"?window.__WX_MER_DEBUG__:void 0)??!1;ut&&pt("\u{1F41B} [DEBUG MODE]",window.__WX_MER_DEBUG__);function pt(...e){ue(...e)}var T={debug:(...e)=>{(window.__WX_MER_DEBUG__??0)>=2&&ue("[MER] [DEBUG]",...e)},log:(...e)=>{(window.__WX_MER_DEBUG__??0)>=1&&ue("[MER]",...e)}},Y=typeof window<"u"?window.navigator:void 0,Rt=/miniProgram/i.test(typeof Y<"u"?Y.userAgent:""),G=!1;typeof document>"u"||document.addEventListener("WeixinJSBridgeReady",()=>{G=window.__wxjs_environment==="miniprogram"});function De(e){return e=(e||Y?.userAgent||"").toLowerCase(),/android|adr/i.test(e)}function Pe(e){return e=(e||Y?.userAgent||"").toLowerCase(),/iphone|ipad|ipod/i.test(e)}var pe={isAndroid:De(),isIOS:Pe(),desc:`${De()?"Android":Pe()?"iOS":"Unknown"} Mini Program`};function k(e,t=!0){let n=[],i=e;for(;i&&i.nodeType===Node.ELEMENT_NODE&&!(t&&(i.parentNode instanceof ShadowRoot||i.parentNode instanceof HTMLIFrameElement));){let r=0,c=i.previousSibling;for(;c;)c.nodeType===Node.ELEMENT_NODE&&c.nodeName===i.nodeName&&r++,c=c.previousSibling;let d=i.nodeName.toLowerCase(),u=r>0?`[${r+1}]`:"";n.unshift(`${d}${u}`),i=i.parentNode}return n.join("/")}Ce();var q={currentIframe:null},U="wx-mer-highlight-container";function H(e={doHighlightElements:!0,focusHighlightIndex:-1,viewportExpansion:0,debugMode:!1,recordRects:!1,skipIframe:!1,recordAll:!1,includeOffscreenElements:!1}){let{doHighlightElements:t,focusHighlightIndex:n=-1,viewportExpansion:i=0,debugMode:r}=e,c=0,d=document.getElementById(U);d&&d.remove();let u={nodeProcessing:[],treeTraversal:[],highlighting:[],current:null},w=window.innerWidth/2,E=window.innerHeight/2,g=document.elementFromPoint(w,E);for(;g&&!["body","iframe"].includes(g?.tagName.toLowerCase());)g=g.parentElement;let N=g;q.currentIframe=N;function ge(o){u[o]=u[o]||[],u[o].push(performance.now())}function V(o){let s=u[o].pop();return performance.now()-s}let l=r?{buildDomTreeCalls:0,timings:{buildDomTree:0,highlightElement:0,isInteractiveElement:0,isElementVisible:0,isTopElement:0,isInExpandedViewport:0,isTextNodeVisible:0,getEffectiveScroll:0},cacheMetrics:{boundingRectCacheHits:0,boundingRectCacheMisses:0,computedStyleCacheHits:0,computedStyleCacheMisses:0,getBoundingClientRectTime:0,getComputedStyleTime:0,boundingRectHitRate:0,computedStyleHitRate:0,overallHitRate:0,clientRectsCacheHits:0,clientRectsCacheMisses:0},nodeMetrics:{totalNodes:0,processedNodes:0,skippedNodes:0},buildDomTreeBreakdown:{totalTime:0,totalSelfTime:0,buildDomTreeCalls:0,domOperations:{getBoundingClientRect:0,getComputedStyle:0},domOperationCounts:{getBoundingClientRect:0,getComputedStyle:0}}}:null;function xt(o){return r?function(...s){let p=performance.now(),a=o.apply(this,s),v=performance.now()-p;return a}:o}function Q(o,s){if(!r)return o();let p=performance.now(),a=o(),v=performance.now()-p;return l&&s in l.buildDomTreeBreakdown.domOperations&&(l.buildDomTreeBreakdown.domOperations[s]+=v,l.buildDomTreeBreakdown.domOperationCounts[s]++),a}let I={boundingRects:new WeakMap,clientRects:new WeakMap,computedStyles:new WeakMap,clearCache:()=>{I.boundingRects=new WeakMap,I.clientRects=new WeakMap,I.computedStyles=new WeakMap}};function F(o){if(!o)return null;if(I.boundingRects.has(o))return r&&l&&l.cacheMetrics.boundingRectCacheHits++,I.boundingRects.get(o);r&&l&&l.cacheMetrics.boundingRectCacheMisses++;let s;if(r){let p=performance.now();s=o.getBoundingClientRect();let a=performance.now()-p;l&&(l.buildDomTreeBreakdown.domOperations.getBoundingClientRect+=a,l.buildDomTreeBreakdown.domOperationCounts.getBoundingClientRect++)}else s=o.getBoundingClientRect();return s&&I.boundingRects.set(o,s),s}function Z(o){if(!o)return null;if(I.computedStyles.has(o))return r&&l&&l.cacheMetrics.computedStyleCacheHits++,I.computedStyles.get(o);r&&l&&l.cacheMetrics.computedStyleCacheMisses++;let s;if(r){let p=performance.now();s=window.getComputedStyle(o);let a=performance.now()-p;l&&(l.buildDomTreeBreakdown.domOperations.getComputedStyle+=a,l.buildDomTreeBreakdown.domOperationCounts.getComputedStyle++)}else s=window.getComputedStyle(o);return s&&I.computedStyles.set(o,s),s}function Ze(o){if(!o)return null;if(I.clientRects.has(o))return r&&l&&l.cacheMetrics.clientRectsCacheHits++,I.clientRects.get(o);r&&l&&l.cacheMetrics.clientRectsCacheMisses++;let s=Array.from(o.getClientRects());return s&&I.clientRects.set(o,s),s}let A={},ee={current:0};function be(o,s,p=null){if(!o)return s;try{let a=document.getElementById(U);a||(a=document.createElement("div"),a.id=U,a.style.position="fixed",a.style.pointerEvents="none",a.style.top="0",a.style.left="0",a.style.width="100%",a.style.height="100%",a.style.zIndex="2147483647",document.body.appendChild(a));let v=Q(()=>o.getBoundingClientRect(),"getBoundingClientRect");if(!v)return s;let h=["#FF0000","#00FF00","#0000FF","#FFA500","#800080","#008080","#FF69B4","#4B0082","#FF4500","#2E8B57","#DC143C","#4682B4"],f=s%h.length,m=h[f],_=`${m}1A`,b=document.createElement("div");b.style.position="fixed",b.style.border=`2px solid ${m}`,b.style.backgroundColor=_,b.style.pointerEvents="none",b.style.boxSizing="border-box";let O={x:0,y:0};if(p){let L=p.getBoundingClientRect();O.x=L.left,O.y=L.top}let C=v.top+O.y,D=v.left+O.x;b.style.top=`${C}px`,b.style.left=`${D}px`,b.style.width=`${v.width}px`,b.style.height=`${v.height}px`;let y=document.createElement("div");y.className="playwright-highlight-label",y.style.position="fixed",y.style.background=m,y.style.color="white",y.style.padding="1px 4px",y.style.borderRadius="4px",y.style.fontSize=`${Math.min(12,Math.max(8,v.height/2))}px`,y.textContent=s.toString();let R=20,$=16,we=C+2,ye=D+v.width-R-2;(v.width<R+4||v.height<$+4)&&(we=C-$-2,ye=D+v.width-R),y.style.top=`${we}px`,y.style.left=`${ye}px`,a.appendChild(b),a.appendChild(y);let xe=()=>{let L=o.getBoundingClientRect(),j={x:0,y:0};if(p){let Me=p.getBoundingClientRect();j.x=Me.left,j.y=Me.top}let te=L.top+j.y,ne=L.left+j.x;b.style.top=`${te}px`,b.style.left=`${ne}px`,b.style.width=`${L.width}px`,b.style.height=`${L.height}px`;let _e=te+2,Te=ne+L.width-R-2;(L.width<R+4||L.height<$+4)&&(_e=te-$-2,Te=ne+L.width-R),y.style.top=`${_e}px`,y.style.left=`${Te}px`};return window.addEventListener("scroll",xe),window.addEventListener("resize",xe),s+1}finally{V("highlighting")}}function et(o){try{let s=document.createRange();s.selectNodeContents(o);let p=s.getBoundingClientRect();if(p.width===0||p.height===0)return!1;let a=!(p.bottom<-i||p.top>window.innerHeight+i||p.right<-i||p.left>window.innerWidth+i),v=o.parentElement;if(!v)return!1;try{return a&&v.checkVisibility({checkOpacity:!0,checkVisibilityCSS:!0})}catch{let f=window.getComputedStyle(v);return a&&f.display!=="none"&&f.visibility!=="hidden"&&f.opacity!=="0"}}catch(s){return console.warn("Error checking text node visibility:",s),!1}}function tt(o){if(!o||!("tagName"in o))return!1;let s=new Set(["body","div","main","article","section","nav","header","footer","wx-tab-bar-wrapper"]),p=o.tagName.toLowerCase(),a=p.startsWith("wx-");return s.has(p)||a?!0:!new Set(["head","svg","script","style","link","meta","noscript","template"]).has(p)}function nt(o){let s=Z(o);return s.height==="auto"&&s.width==="auto"?s.visibility!=="hidden"&&s.display!=="none":o.offsetWidth>0&&o.offsetHeight>0&&s.visibility!=="hidden"&&s.display!=="none"}function ot(o){if(!o||o.nodeType!==Node.ELEMENT_NODE)return!1;let s=o.tagName.toLowerCase(),p=Z(o);function a(b){return b.tagName.toLowerCase()==="html"?!1:!!(p&&Le.has(p.cursor))}if(a(o))return!0;if(s==="html")return!1;if(z.has(s)){if(Se.has(p.cursor))return!1;for(let b of Re)if(o.hasAttribute(b)||o.getAttribute(b)==="true"||o.getAttribute(b)==="")return!1;return!(o.disabled||o.readOnly||o.inert)}let h=o.getAttribute("role"),f=o.getAttribute("aria-role"),m=Object.keys(o.__wxElement?.__wxEvents||{}).filter(b=>!["error","load"].includes(b)&&!b.startsWith("_"));if(o.classList&&(o.classList.contains("button")||o.classList.contains("dropdown-toggle")||o.getAttribute("data-index")||o.getAttribute("data-toggle")==="dropdown"||o.getAttribute("aria-haspopup")==="true")||z.has(s)||le.has(h||"")||le.has(f||"")||m.length>0)return!0;try{if(typeof getEventListeners=="function"){let C=getEventListeners(o),D=["click","mousedown","mouseup","dblclick"];for(let y of D)if(C[y]&&C[y].length>0)return!0}let b=window.getEventListenersForNode;if(typeof b=="function"){let C=b(o),D=["click","mousedown","mouseup","keydown","keyup","submit","change","input","focus","blur"];for(let y of D)for(let R of C)if(R.type===y)return!0}let O=["onclick","onmousedown","onmouseup","ondblclick"];for(let C of O)if(o.hasAttribute(C)||typeof o[C]=="function")return!0}catch{}return!1}function Ee(o){if(i===-1)return!0;let s=Ze(o);if(!s||s.length===0)return!1;let p=o.ownerDocument;if(p!==window.document&&p!==N?.contentDocument)return!1;let a=o.getRootNode();if(a instanceof ShadowRoot||a===o.ownerDocument){let f=s[Math.floor(s.length/2)].left+s[Math.floor(s.length/2)].width/2,m=s[Math.floor(s.length/2)].top+s[Math.floor(s.length/2)].height/2;try{let _=Q(()=>a.elementFromPoint(f,m),"elementFromPoint");if(!_)return!1;let b=_;if(o.contains(_)||_.contains(o))return!0;for(;b&&b!==a;){if(b===o)return!0;b=b.parentElement}return!1}catch{return!0}}let v=s[Math.floor(s.length/2)].left+s[Math.floor(s.length/2)].width/2,h=s[Math.floor(s.length/2)].top+s[Math.floor(s.length/2)].height/2;try{let f=document.elementFromPoint(v,h);if(!f)return!1;let m=f;if(o.contains(f))return!0;for(;m&&m!==document.documentElement;){if(m===o)return!0;m=m.parentElement}return!1}catch{return!0}}function rt(o,s){if(s===-1)return!0;let p=o.getClientRects();if(!p||p.length===0){let a=F(o);return!a||a.width===0||a.height===0?!1:!(a.bottom<-s||a.top>window.innerHeight+s||a.right<-s||a.left>window.innerWidth+s)}for(let a of p)if(!(a.width===0||a.height===0)&&!(a.bottom<-s||a.top>window.innerHeight+s||a.right<-s||a.left>window.innerWidth+s))return!0;return!1}function _t(o){let s=o,p=0,a=0;return Q(()=>{for(;s&&s!==document.documentElement;)(s.scrollLeft||s.scrollTop)&&(p+=s.scrollLeft,a+=s.scrollTop),s=s.parentElement;return p+=window.scrollX,a+=window.scrollY,{scrollX:p,scrollY:a}},"scrollOperations")}function Tt(o){return o.offsetWidth>0&&o.offsetHeight>0&&!o.hasAttribute("hidden")&&o.style.display!=="none"&&o.style.visibility!=="hidden"}function P(o,s=null){if(r&&l.nodeMetrics.totalNodes++,!o||o.id===U)return r&&l.nodeMetrics.skippedNodes++,null;if(o===document){let h={tagName:"html",attributes:{},xpath:"html",children:[]};for(let m of o.childNodes){let _=P(m,s);_&&h.children?.push(_)}let f=`${ee.current++}`;return A[f]=h,r&&l.nodeMetrics.processedNodes++,f}if(o.nodeType!==Node.ELEMENT_NODE&&o.nodeType!==Node.TEXT_NODE)return r&&l.nodeMetrics.skippedNodes++,null;if(o.nodeType===Node.TEXT_NODE){let h=o.textContent.trim();if(!h)return r&&l.nodeMetrics.skippedNodes++,null;let f=o.parentElement;if(!f||f.tagName.toLowerCase()==="script")return r&&l.nodeMetrics.skippedNodes++,null;let m=`${ee.current++}`;return A[m]={type:"TEXT_NODE",text:h,isVisible:et(o)},r&&l.nodeMetrics.processedNodes++,m}if(o.nodeType===Node.ELEMENT_NODE&&!tt(o))return r&&l.nodeMetrics.skippedNodes++,null;if(i!==-1){let h=F(o),f=Z(o),m=f&&(f.position==="fixed"||f.position==="sticky"),_=o.offsetWidth>0||o.offsetHeight>0||f&&f.height==="auto"&&f.width==="auto";if(!h||!m&&!_&&(h.bottom<-i||h.top>window.innerHeight+i||h.right<-i||h.left>window.innerWidth+i))return r&&l.nodeMetrics.skippedNodes++,null}let p=k(o,!0);s&&(p=`iframe/${p}`);let a={tagName:o.tagName.toLowerCase(),attributes:{},xpath:p,children:[]};if(ke(o)||o.tagName.toLowerCase()==="iframe"||o.tagName.toLowerCase()==="body"){let h=o.getAttributeNames?.()||[],f=["style"];for(let m of h)f.includes(m)||(a.attributes[m]=o.getAttribute(m))}if(o.tagName.toLowerCase()==="wx-scroll-view"){let h=o.getAttribute("scroll-x"),f=o.getAttribute("scroll-y");a.attributes["scroll-x"]=h===null?"false":h===""?"true":h,a.attributes["scroll-y"]=f===null?"false":f===""?"true":f}if(o.nodeType===Node.ELEMENT_NODE&&(a.tagName==="wx-tab-bar-wrapper"?(a.isVisible=!0,a.isTopElement=!0,a.isInteractive=!0,a.isInViewport=!0):a.isVisible=nt(o),window.getEventListenersForNode&&!G&&(a.listeners=window.getEventListenersForNode(o).map(h=>h.type)),(e.recordAll||e.includeOffscreenElements)&&(a.wxEvents=Object.keys(o.__wxElement?.__wxEvents||{}).filter(h=>!["error","load"].includes(h)&&!h.startsWith("_")),a.isInteractive=a.wxEvents.length>0||(a.listeners||[])?.length>0),a.isVisible&&(e.recordRects&&(a.rect=B(F(o))),a.isInViewport=rt(o,i),a.isInViewport?a.isTopElement=Ee(o):a.isTopElement=!1,a.isTopElement&&(a.isInteractive=ot(o),a.isInteractive&&(a.highlightIndex=c++,!e.recordAll&&!e.includeOffscreenElements&&(a.wxEvents=Object.keys(o.__wxElement?.__wxEvents||{}).filter(h=>!["error","load"].includes(h)&&!h.startsWith("_"))),t&&(n>=0?n===a.highlightIndex&&be(o,a.highlightIndex,s):be(o,a.highlightIndex,s)))))),o.tagName){let h=o.tagName.toLowerCase();if(h==="iframe"&&!e.skipIframe)try{let f=o.contentDocument||o.contentWindow?.document;if(f)for(let m of f.childNodes){let _=P(m,o);_&&a.children?.push(_)}}catch(f){console.warn("Unable to access iframe:",f)}else if(o.isContentEditable||o.getAttribute("contenteditable")==="true"||o.id==="tinymce"||o.classList.contains("mce-content-body")||h==="body"&&o.getAttribute("data-id")?.startsWith("mce_"))for(let f of o.childNodes){let m=P(f,s);m&&a.children?.push(m)}else if(o.shadowRoot){a.shadowRoot=!0;for(let f of o.shadowRoot.childNodes){let m=P(f,s);m&&a.children?.push(m)}}else for(let f of o.childNodes){let m=P(f,s);m&&a.children?.push(m)}}if(a.tagName==="a"&&a.children?.length===0&&!a.attributes?.href)return r&&l.nodeMetrics.skippedNodes++,null;let v=`${ee.current++}`;return A[v]=a,r&&l.nodeMetrics.processedNodes++,v}let ve=P(document);if(I.clearCache(),r&&l){Object.keys(l.timings).forEach(p=>{l.timings[p]=l.timings[p]/1e3}),Object.keys(l.buildDomTreeBreakdown).forEach(p=>{typeof l.buildDomTreeBreakdown[p]=="number"&&(l.buildDomTreeBreakdown[p]=l.buildDomTreeBreakdown[p]/1e3)}),l.buildDomTreeBreakdown.buildDomTreeCalls>0&&(l.buildDomTreeBreakdown.averageTimePerNode=l.buildDomTreeBreakdown.totalTime/l.buildDomTreeBreakdown.buildDomTreeCalls),l.buildDomTreeBreakdown.timeInChildCalls=l.buildDomTreeBreakdown.totalTime-l.buildDomTreeBreakdown.totalSelfTime,Object.keys(l.buildDomTreeBreakdown.domOperations).forEach(p=>{let a=l.buildDomTreeBreakdown.domOperations[p],v=l.buildDomTreeBreakdown.domOperationCounts[p];v>0&&(l.buildDomTreeBreakdown.domOperations[`${p}Average`]=a/v)});let o=l.cacheMetrics.boundingRectCacheHits+l.cacheMetrics.boundingRectCacheMisses,s=l.cacheMetrics.computedStyleCacheHits+l.cacheMetrics.computedStyleCacheMisses;o>0&&(l.cacheMetrics.boundingRectHitRate=l.cacheMetrics.boundingRectCacheHits/o),s>0&&(l.cacheMetrics.computedStyleHitRate=l.cacheMetrics.computedStyleCacheHits/s),o+s>0&&(l.cacheMetrics.overallHitRate=(l.cacheMetrics.boundingRectCacheHits+l.cacheMetrics.computedStyleCacheHits)/(o+s))}return r?{rootId:ve,map:A,perfMetrics:l,isTopElement:Ee,getCachedBoundingRect:F}:{rootId:ve,map:A}}function Oe(e){let t=[];return Object.keys(e.map).forEach(n=>{let i=e.map[n];i.attributes||(i.attributes={}),i.isTopElement&&i.isInViewport&&t.push(i)}),t}function He(e){return{left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}}function Ae(e,t){let n=Math.max(e.left,t.left),i=Math.max(e.top,t.top),r=Math.min(e.right,t.right),c=Math.min(e.bottom,t.bottom);return n>=r||i>=c?null:{left:n,top:i,right:r,bottom:c,width:r-n,height:c-i}}function dt(e,t){let n=Ae(e,t);if(!n)return[e];let i=[];return e.top<n.top&&i.push({left:e.left,top:e.top,right:e.right,bottom:n.top,width:e.width,height:n.top-e.top}),e.bottom>n.bottom&&i.push({left:e.left,top:n.bottom,right:e.right,bottom:e.bottom,width:e.width,height:e.bottom-n.bottom}),e.left<n.left&&i.push({left:e.left,top:n.top,right:n.left,bottom:n.bottom,width:n.left-e.left,height:n.height}),e.right>n.right&&i.push({left:n.right,top:n.top,right:e.right,bottom:n.bottom,width:e.right-n.right,height:n.height}),i}function Xe(e){if(!e)return{x:0,y:0};let t=He(e.getBoundingClientRect()),n=Array.from(e.children).filter(c=>c.nodeType!==Node.TEXT_NODE);if(n.length===0)return{x:Math.ceil(t.left+t.width/2),y:Math.ceil(t.top+t.height/2)};let i=n.map(c=>He(c.getBoundingClientRect())).map(c=>Ae(t,c)).filter(c=>c!==null),r=[t];for(let c of i){let d=[];for(let u of r)d.push(...dt(u,c));r=d}if(r.length>0){let c=r.reduce((d,u)=>u.width*u.height>d.width*d.height?u:d);return{x:Math.ceil(c.left+c.width/2),y:Math.ceil(c.top+c.height/2)}}return{x:Math.ceil(t.left+t.width/2),y:Math.ceil(t.top+t.height/2)}}function de(){let e=window.innerWidth/2,t=window.innerHeight/2,n=document.elementFromPoint(e,t);return n?n.contentDocument:document}function S(e,t=de()){let n=document.evaluate(e,t||document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null);return console.log("parent:",t),console.log("xPathResult:",n),n.singleNodeValue}function Be(e){let t=S(e),n=t?.getBoundingClientRect();if(!n){console.error("\u672A\u627E\u5230\u5143\u7D20");return}let{left:i,top:r,width:c,height:d}=n,u=i+c/2,w=r+d/2,{x:E,y:g}=Xe(t);return{centerX:u,centerY:w,clickableX:E,clickableY:g,left:i,top:r,width:c,height:d}}function ft(e){let t=e.getBoundingClientRect(),n=window.innerWidth||document.documentElement.clientWidth,i=window.innerHeight||document.documentElement.clientHeight;return t.top>=0&&t.left>=0&&t.bottom<=i&&t.right<=n}function We(e){e?.scrollIntoView({behavior:"instant",block:"center",inline:"center"})}function Ve(e){let t=S(e);t?We(t):console.error("\u672A\u627E\u5230\u5143\u7D20")}function ht(e){ft(e)?console.log("\u5143\u7D20\u5DF2\u5728\u89C6\u56FE\u4E2D\uFF0C\u65E0\u9700\u6EDA\u52A8"):We(e)}function Fe(e){let t=S(e);if(!t){console.error("\u672A\u627E\u5230\u5143\u7D20");return}ht(t)}function J(e={doHighlightElements:!1}){let t=H({doHighlightElements:e?.doHighlightElements||!1,focusHighlightIndex:-1,viewportExpansion:0,debugMode:!1,recordRects:!0,recordAll:!1});return Oe(t).map(r=>{let{rect:c,tagName:d,wxEvents:u,listeners:w,xpath:E}=r,g={isInteractive:r.isInteractive,rect:c,tagName:d,wxEvents:u,listeners:w,xpath:E};return r.tagName?.toLowerCase()==="wx-scroll-view"&&(g.attributes=r.attributes),g})}var $e=["id","xpath","textContent","attributes","wxEvents","isVisible","isInteractive","listeners"];function B(e){return{x:Math.round(e.x),y:Math.round(e.y),left:Math.round(e.left),top:Math.round(e.top),width:Math.round(e.width),height:Math.round(e.height)}}function je(e){let t={tagName:"root",attributes:{},children:[]};for(let n of e){if(typeof n.id>"u")continue;let i=n.xpath?.split("/").filter(d=>d.length>0);if(!i||i.length===0){console.log("\u8282\u70B9\u6CA1\u6709 xpath",n);continue}let r=t,c=n.textContent||"";for(let d=0;d<i.length;d++){let u=d===i.length-1,w=i[d];if(w.startsWith("@")){r.attributes=n.attributes||{};break}let E=r.children?.find(g=>g.tagName===w);E||(E={tagName:w,attributes:{},children:[]},r.children.push(E)),r=E,u&&$e.forEach(g=>{n[g]!==void 0&&(E[g]=n[g])})}}return t.children[0]}var mt=["id","name","placeholder","data-event-opts"];function ze(e,t){let n=["iconfont","close","guanbi","active"];return e==="class"&&t?.[e]?n.some(i=>t[e].includes(i)):e.startsWith("aria-")||e.startsWith("data-")||mt.includes(e)||e.startsWith("scroll-")}function Ye(e){return{all:e=e||new Map,on:function(t,n){var i=e.get(t);i?i.push(n):e.set(t,[n])},off:function(t,n){var i=e.get(t);i&&(n?i.splice(i.indexOf(n)>>>0,1):e.set(t,[]))},emit:function(t,n){var i=e.get(t);i&&i.slice().map(function(r){r(n)}),(i=e.get("*"))&&i.slice().map(function(r){r(t,n)})}}}var gt=Ye();async function Ge(e,t){if(pe.isAndroid){if(e==="report")return PageInfoReporterAndroid.report(t.action,t.type,t.csv,t.interactiveInfo||"",t.nativeComponentInfo||"",t.duration||0);if(e==="reportByNative")return PageInfoReporterAndroid.reportByNative(JSON.stringify(t));if(e==="recognizeImagesByNative")return PageInfoReporterAndroid.recognizeImagesByNative(t.infoStr);if(e==="getNativeComponentInfo")return PageInfoReporterAndroid.getNativeComponentInfo()}else{if(pe.isIOS)return new Promise((n,i)=>{T.debug("callNativeFunction",e,t),window.pageInfoReporter.callMethod(e,t,r=>{n(r)})});T.log("callNativeFunction not implemented for this platform",e,t)}}async function bt(e){try{let t=await Ge("recognizeImagesByNative",{infoStr:JSON.stringify(e.map(i=>({src:i.attributes?.src})))})||"[]",n=JSON.parse(t);n.length===e.length?e.forEach((i,r)=>{let c=n[r].label;c&&(i.attributes["aria-label"]=c)}):T.debug("\u{1F41B} \u8BC6\u522B\u56FE\u7247\u5931\u8D25\uFF0C\u957F\u5EA6\u4E0D\u5339\u914D",t)}catch(t){T.log("\u{1F41B} \u8BC6\u522B\u56FE\u7247\u5931\u8D25",t)}finally{T.debug("\u{1F41B} \u8BC6\u522B\u56FE\u7247",e.length,e,e.map(t=>t.attributes?.src)),e.forEach(t=>{t.attributes&&delete t.attributes.src})}}async function Ue(e,t={recordAll:!1,includeOffscreenElements:!1}){T.log("\u{1F41B} [getReportedElements] options:",t);let n=[],i=[],r=[],c=t.includeOffscreenElements||t.recordAll;return Object.keys(e.map).forEach(d=>{let u=e.map[d];if(u.attributes||(u.attributes={}),u.children&&u.children.length&&u.children.some(g=>{let N=e.map[g].type==="TEXT_NODE";return c?N:N&&e.map[g].isVisible})&&(u.textContent=u.children.map(g=>e.map[g].text||e.map[g].el?.textContent).join(" "),delete u.children),u.id=d,(t.includeOffscreenElements||u.isInViewport)&&u.isTopElement){let E=u.isVisible&&!!u.textContent,g=u.isInteractive&&((u.wxEvents||[]).length>0||(u.listeners||[]).length>0),N=!1;u.tagName==="wx-image"&&u.attributes&&"src"in u.attributes&&(u.attributes["aria-label"]?delete u.attributes.src:r.push(u),N=!!(u.isVisible||u.isInteractive)),(E||g||N)&&i.push(u),g&&n.push(u)}}),await bt(r),{interactiveElements:n,reportedElements:i,imageElements:r}}function he(e,t=0){let n="  ".repeat(t),i=Et(e),r=e.children&&e.children.length>0,c=e.textContent||e.innerText||e.nodeValue||"",d=c!=="",u=e.tagName?.toLowerCase()||e.nodeName?.toLowerCase();if(!u)return"";u.startsWith("#")&&(u=u.slice(1));let w=`${n}<${u}${i}`;if(!r&&!d)return`${w} />
`;if(w+=">",d&&!r){let E=c;return c.includes(`
`)&&(E=c.split(`
`).join("  ")),`${w}${K(E)}</${u}>
`}return w+=`
`,e.children&&(e.children.forEach(E=>{w+=he(E,t+1)}),d&&(w+=`${n}  ${K(c)}
`)),w+=`${n}</${u}>
`,w}function Et(e){if(!e)return"";let t=[],n=e.id||e.nodeId;if(n&&t.push(`id="${n}"`),typeof e.attributes=="object"&&e.attributes)if(Array.isArray(e.attributes))e.attributes&&e.attributes.length>0&&t.push(`attr="${fe(e.attributes)}"`);else for(let c in e.attributes)e.attributes[c]!==void 0&&t.push(`${c}="${K(e.attributes[c])}"`);let i=e.events||e.wxEvents;i&&i.length>0&&t.push(`event="${fe(i)}"`);let r=e.listeners;return r&&r.length>0&&t.push(`listener="${fe(r)}"`),e.classList&&e.classList.length>0&&t.push(`class="${e.classList.join(" ")}"`),e.zIndex&&t.push(`z="${e.zIndex}"`),t.length>0?` ${t.join(" ")}`:""}function fe(e){return`[${e.map(t=>`'${K(t)}'`).join(", ")}]`}function K(e){return e?e.replace(/[&<>'"]/g,t=>{switch(t){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case'"':return"&quot;";case"'":return"&apos;";default:return t}}):""}function me(){let e=window.innerWidth/2,t=window.innerHeight/2,n=document.elementFromPoint(e,t);for(;n&&!["body","iframe"].includes(n?.tagName.toLowerCase());)n=n.parentElement;let r=n?.getBoundingClientRect();return{offsetX:r?.x||0,offsetY:r?.y||0,rect:B(r)}}function qe(){let{offsetX:e,offsetY:t}=me(),n=J({});return n.forEach(i=>{i.rect&&(i.rect.x+=e,i.rect.y+=t)}),n}function Je(e={doHighlightElements:!1}){let t=H({doHighlightElements:e.doHighlightElements,focusHighlightIndex:-1,viewportExpansion:0,skipIframe:!0,recordRects:!0}),n=[];return Object.keys(t.map).forEach(i=>{let r=t.map[i];r.isTopElement&&r.isInViewport&&r.isVisible&&r.isInteractive&&n.push(r)}),n.map(i=>({tagName:i.tagName,rect:i.rect}))}function vt(e){let t=e.split("/");if(t[1]==="native"&&t[2]==="tab-bar"){let i=t[3].split("tabbar-item")[1].replace("[","").replace("]","");return document.querySelector(`#container > div.tab_bar > div > div.tabbar_item.tabbar_item_${i}`)}}function Ke(e){let t=vt(e.xpath);if(!t){X.info("native element not found");return}e.event==="click"&&t.click()}function Qe(){let e=[],t=S('/html/body//*[text()="\u540C\u610F"]');t&&e.push(t);let n=S('/html/body//*[text()="\u62D2\u7EDD"]');n&&e.push(n),e.forEach(i=>{i.setAttribute("event","tap")}),console.log("tapElements",e)}var wt={DEBUG_LEVEL:0,getElementByXpath:S,getCurrentDocument:de,getElementPosByXpath:Be,operateNativeElement:Ke,getDomXml:yt,getAllElementsRects:J,getIframeOffset:me,getAllElementsRectsWithOffset:qe,getAllNativeElementsRects:Je,scrollIntoViewByXpath:Ve,scrollIntoViewIfNeededByXpath:Fe};window.__WX_WM_SANDBOX__=wt;async function yt(){Qe();let e=H({doHighlightElements:window.__WX_WM_SANDBOX__.DEBUG_LEVEL>=2,focusHighlightIndex:-1,viewportExpansion:0,debugMode:window.__WX_WM_SANDBOX__.DEBUG_LEVEL>=2}),{reportedElements:t}=await Ue(e),n=t.map(E=>{let{attributes:g={},...N}=E,ge=Object.keys(g).reduce((V,l)=>(ze(l,g)&&(V[l]=g[l]),V),{});return{...N,attributes:ge}}),i=je(n),r=he(i);X.debug("xml",r);let c=[];n.forEach(E=>{if(E.xpath?.startsWith("html/body")){let g=S(E.xpath,document)?.parentElement;g?.classList.contains("tabbar_item")&&(E.classList=g.classList,c.push(E))}}),X.debug("nativeElements",c);let d="<tab-bar>";c.forEach(E=>{let g=E.classList[1]?.replace("tabbar_item_",""),N=E.textContent;d+=`  <tabbar-item index="${g}" event="['tap']">${N}</tabbar-item>
`}),d+="</tab-bar>";let u=c.length>0?`<native>${d}</native>`:"";return`<page>
  ${r}${u}
</page>`}})();