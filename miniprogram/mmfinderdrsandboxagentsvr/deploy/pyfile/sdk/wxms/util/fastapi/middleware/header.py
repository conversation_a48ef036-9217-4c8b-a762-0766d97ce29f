"""
header 相关的 middleware
"""

from starlette.datastructures import MutableHeaders
from wxms.env import MODULE, POD_IP
from wxms.util.context_util import context_util


class InsertResponseHeadersMiddleware:
    """
    Response 添加 header
    """

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            return await self.app(scope, receive, send)

        async def send_with_insert_headers(message):
            if message["type"] == "http.response.start":
                headers = MutableHeaders(scope=message)
                headers.append("trace-id", context_util.get_trace_id())
                headers.append(f"x-{MODULE.lower()}-ip", POD_IP)
            await send(message)

        await self.app(scope, receive, send_with_insert_headers)
