"""
日志相关的 middleware
"""

import time

from starlette.requests import Request
from uvicorn.protocols.utils import ClientDisconnected
from wxms.logger import logger


class LogMiddleware:
    """
    日志打印关键信息
    """

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            return await self.app(scope, receive, send)

        start_time = time.time()
        request = Request(scope)
        temp_dict = {}

        async def send_wrapper(response):
            if response["type"] == "http.response.start":
                temp_dict["resp_http_status_code"] = response.get("status", None)
            try:
                await send(response)
            except ClientDisconnected:
                logger.warning(
                    "Get ClientDisconnected Exception in LogOperationInfoMiddleware"
                )
            if (
                response["type"] == "http.response.body"
                and response.get("more_body", False) is False
            ):
                process_time = (time.time() - start_time) * 1000
                resp_http_status_code = temp_dict.get(
                    "resp_http_status_code", "Unknown"
                )
                logger.info(
                    "process_time=%.2fms - %s %s %s",
                    process_time,
                    request.method,
                    request.url.path,
                    resp_http_status_code,
                )

        await self.app(scope, receive, send_wrapper)
