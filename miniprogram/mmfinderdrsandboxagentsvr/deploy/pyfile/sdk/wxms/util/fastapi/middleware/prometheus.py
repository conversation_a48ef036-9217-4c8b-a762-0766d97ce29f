"""
prometheus 相关的 middleware
"""

from starlette_exporter import PrometheusMiddleware as StarlettePrometheusMiddleware
from wxms.logger import app_env, app_name
from wxms.util.context_util import context_util


class PrometheusMiddleware(StarlettePrometheusMiddleware):
    """
    文档 https://github.com/stephenhillier/starlette_exporter
    """

    def __init__(self, app, skip_paths: list[str] | None = None):
        if skip_paths is None:
            skip_paths = ["/metrics", "/v1/ops/health"]
        temp_dict = {
            "app": app,
            "app_name": app_name,
            "prefix": "fastapi",
            "buckets": [0.5, 2, 7, 10, 20, 40],
            "skip_paths": skip_paths,
            "skip_methods": ["OPTIONS"],
            "always_use_int_status": True,
            "labels": {
                "service_name": app_name,
                "service_env": app_env,
            },
            "exemplars": lambda: {"traceId": context_util.get_trace_id()},
        }
        super().__init__(**temp_dict)

    async def __call__(self, scope, receive, send):
        await super().__call__(scope, receive, send)
