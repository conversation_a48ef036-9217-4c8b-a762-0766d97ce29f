"""
fastapi 工具类
"""

import traceback
from contextlib import asynccontextmanager
from typing import Callable

from fastapi import FastAP<PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from wxms.logger import close_logger, logger
from wxms.model.api.common import BaseHttpResponse
from wxms.model.exception.error_code_exception import BaseErrorCodeException, ErrorCode
from wxms.model.exception.http_exception import BaseHTTPException
from wxms.model.exception.internal_exception import BaseInternalException
from wxms.util.context_util import context_util
from wxms.util.fastapi.endpoint.ops import router as ops_router
from wxms.util.fastapi.endpoint.prometheus import router as prometheus_router
from wxms.util.fastapi.middleware.header import (
    InsertResponseHeadersMiddleware,
)
from wxms.util.fastapi.middleware.log import (
    LogMiddleware,
)
from wxms.util.fastapi.middleware.prometheus import (
    PrometheusMiddleware,
)


class FastAPIUtil:
    """
    fastapi 工具类
    """

    def __init__(self):
        pass

    def get_lifespan(
        self,
        customized_fn_for_start: Callable | None = None,
        customized_fn_for_end: Callable | None = None,
    ):
        """
        FastAPI 的生命周期，主要用于在启动前和启动后做相应的处理
        """

        @asynccontextmanager
        async def lifespan(_: FastAPI):
            logger.info("server starting, start to init all components")
            if customized_fn_for_start is not None:
                logger.info("customized_fn_for_start start")
                customized_fn_for_start()
                logger.info("customized_fn_for_start success")
            yield
            logger.info("server exiting, release all components")
            if customized_fn_for_end is not None:
                logger.info("customized_fn_for_end start")
                customized_fn_for_end()
                logger.info("customized_fn_for_end success")
            logger.info("close_logger start")
            close_logger()
            logger.info("close_logger success")

        return lifespan

    def init_exception_handler(self, app: FastAPI, set_cors_header: bool = False):
        """
        初始化 app 的 middleware

        :param app: FastAPI 的对象
        :param set_cors_header: default_exception_handler 处理过程中是否要根据 request 的跨域 header 设置返回的跨域
        """

        @app.exception_handler(Exception)
        async def default_exception_handler(request: Request, exc: Exception):
            body, body_message = await context_util.get_request_body(request)
            logger.error(
                "default_exception_handler to process request",
                extra={
                    "customized_data_info": {
                        "url": request.url,
                        "body": body,
                        "body_message": body_message,
                        "exception": str(exc),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
            resp = JSONResponse(
                status_code=200,
                content=BaseHttpResponse(
                    code=ErrorCode.UNKNOWN.value.error_code,
                    message=ErrorCode.UNKNOWN.value.error_message,
                    data=None,
                ).model_dump(by_alias=True),
            )
            origin_in_request_header = request.headers.get("origin")
            if set_cors_header and origin_in_request_header:
                resp.headers["Access-Control-Allow-Origin"] = origin_in_request_header
                resp.headers["Access-Control-Allow-Credentials"] = "true"
            return resp

        @app.exception_handler(ValueError)
        async def value_error_handler(request: Request, exc: ValueError):
            body, body_message = await context_util.get_request_body(request)
            logger.warning(
                "value_error_handler to process request",
                extra={
                    "customized_data_info": {
                        "url": request.url,
                        "body": body,
                        "body_message": body_message,
                        "exception": str(exc),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
            resp = JSONResponse(
                status_code=422,
                content=BaseHttpResponse(
                    code=422,
                    # message=ErrorCode.UNKNOWN.value.error_message,
                    message=str(exc),
                    data=None,
                ).model_dump(by_alias=True),
            )
            return resp

        @app.exception_handler(RequestValidationError)
        async def request_validation_error_handler(
            request: Request, exc: RequestValidationError
        ):
            body, body_message = await context_util.get_request_body(request)
            logger.warning(
                "request_validation_error_handler to process request",
                extra={
                    "customized_data_info": {
                        "url": request.url,
                        "body": body,
                        "body_message": body_message,
                        "exception": str(exc),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
            resp = JSONResponse(
                status_code=422,
                content=BaseHttpResponse(
                    code=422,
                    # message=ErrorCode.UNKNOWN.value.get("error_message", ""),
                    message=str(exc.errors()),
                    data=None,
                ).model_dump(by_alias=True),
            )
            return resp

        @app.exception_handler(BaseHTTPException)
        async def base_http_exception_handler(request: Request, exc: BaseHTTPException):
            body, body_message = await context_util.get_request_body(request)
            logger.error(
                "base_http_exception_handler to process request",
                extra={
                    "customized_data_info": {
                        "url": request.url,
                        "body": body,
                        "body_message": body_message,
                        "exception": str(exc),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
            resp = JSONResponse(
                status_code=exc.status_code,
                headers=exc.headers,
                content=BaseHttpResponse(
                    code=exc.status_code, message=exc.detail, data=None
                ).model_dump(by_alias=True),
            )
            return resp

        @app.exception_handler(BaseInternalException)
        async def base_internal_exception_handler(
            request: Request, exc: BaseInternalException
        ):
            body, body_message = await context_util.get_request_body(request)
            logger.error(
                "base_internal_exception_handler to process request",
                extra={
                    "customized_data_info": {
                        "url": request.url,
                        "body": body,
                        "body_message": body_message,
                        "exception": str(exc),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
            resp = JSONResponse(
                status_code=200,
                content=BaseHttpResponse(
                    code=ErrorCode.UNKNOWN.value.error_code,
                    message=ErrorCode.UNKNOWN.value.error_message,
                    data=None,
                ).model_dump(by_alias=True),
            )
            return resp

        @app.exception_handler(BaseErrorCodeException)
        async def base_error_code_exception_handler(
            request: Request, exc: BaseErrorCodeException
        ):
            body, body_message = await context_util.get_request_body(request)
            logger.error(
                "base_error_code_exception_handler to process request",
                extra={
                    "customized_data_info": {
                        "url": request.url,
                        "body": body,
                        "body_message": body_message,
                        "exception": str(exc),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
            resp = JSONResponse(
                status_code=200,
                headers=exc.headers,
                content=BaseHttpResponse(
                    code=exc.error_code.value.error_code,
                    message=exc.error_message,
                    data=None,
                ).model_dump(by_alias=True),
            )
            return resp

    def init_middleware(self, app: FastAPI, origins: list[str] | None = None):
        """
        初始化 app 的 middleware

        :param app: FastAPI 的对象
        :param origins: 允许跨域的域名列表
        """
        if origins is None:
            origins = [
                "http://localhost",
                "http://localhost:3000",
                "http://localhost:8080",
                "http://127.0.0.1",
                "http://127.0.0.1:3000",
                "http://127.0.0.1:8080",
            ]
        app.add_middleware(InsertResponseHeadersMiddleware)
        app.add_middleware(PrometheusMiddleware)
        app.add_middleware(LogMiddleware)
        app.add_middleware(
            CORSMiddleware,
            allow_origins=origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    def init_router_common(self, app: FastAPI, prefix: str):
        """
        初始化 app 的通用 router

        :param app: FastAPI 的对象
        :param prefix: ops 接口 path 的前缀
        """
        app.include_router(ops_router, prefix=prefix)
        app.include_router(prometheus_router)


fastapi_util = FastAPIUtil()
