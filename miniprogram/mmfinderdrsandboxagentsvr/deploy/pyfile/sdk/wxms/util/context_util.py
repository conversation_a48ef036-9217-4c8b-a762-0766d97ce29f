"""
上下文工具类
"""

import contextvars
import json
import secrets
import threading
from concurrent.futures import ThreadPoolExecutor
from functools import wraps
from typing import Any, Callable

from fastapi import Request
from opentelemetry import context as otel_context
from opentelemetry import trace
from opentelemetry.trace.propagation.tracecontext import TraceContextTextMapPropagator
from wxms.logger import logger
from wxms.util.common_util import CustomJSONEncoder, common_util
from wxms.util.time_util import time_util


class ContextUtil:
    """
    上下文工具类
    """

    def __init__(self):
        self.sandbox_action_id = contextvars.ContextVar("sandbox_action_id", default="")
        self.sandbox_step_id = contextvars.ContextVar("sandbox_step_id", default="")
        self.sandbox_sub_session_id = contextvars.ContextVar(
            "sandbox_sub_session_id", default=""
        )
        self.zero_trace_id = "00000000000000000000000000000000"

    def add_trace_span(
        self,
        span_name: str | None = None,
        carrier: dict | None = None,
        attributes: dict | None = None,
        record_input: bool = False,
        record_output: bool = False,
    ):
        """
        函数装饰器，用于将函数包装为一个 span（同步）

        :param span_name: span name，不传时默认为函数名
        :param carrier: 带有上下文信息的字典
        :param attributes: span 的 attributes
        :param record_input: 是否记录被装饰函数的输入为 span 的 attribution
            当为 True 时，要求输入可以被 json.dumps，输入仅记录 kwargs
        :param record_output: 是否记录被装饰函数的输出为 span 的 attribution
            当为 True 时，要求输出可以被 json.dumps
        """

        def add_trace_span_wrapper(func):
            @wraps(func)
            def add_trace_span_inner(*args, **kwargs):
                tracer = trace.get_tracer(__name__)
                span_name_value = span_name if span_name else func.__name__
                if span_name_value.startswith("__"):
                    span_name_value = span_name_value[2:]
                elif span_name_value.startswith("_"):
                    span_name_value = span_name_value[1:]
                attributes_value = {}
                if attributes is not None:
                    attributes_value = {
                        f"{span_name_value}.{k}": attributes[k] for k in attributes
                    }
                if record_input:
                    attributes_value["func_input"] = json.dumps(
                        kwargs, cls=CustomJSONEncoder
                    )
                with tracer.start_as_current_span(
                    span_name_value,
                    context=(
                        TraceContextTextMapPropagator().extract(carrier)
                        if carrier is not None
                        else None
                    ),
                    attributes=attributes_value if attributes_value else None,
                ) as span:
                    res = func(*args, **kwargs)
                    if record_output:
                        span.add_event(
                            name="func_output",
                            attributes={
                                "name": "func_output",
                                "data": json.dumps(res, cls=CustomJSONEncoder),
                            },
                        )
                return res

            return add_trace_span_inner

        return add_trace_span_wrapper

    def add_trace_span_for_async(
        self,
        span_name: str | None = None,
        carrier: dict | None = None,
        attributes: dict | None = None,
        record_input: bool = False,
        record_output: bool = False,
    ):
        """
        函数装饰器，用于将函数包装为一个 span（异步）

        :param span_name: span name，不传时默认为函数名
        :param carrier: 带有上下文信息的字典
        :param attributes: span 的 attributes
        :param record_input: 是否记录被装饰函数的输入为 span 的 attribution
            当为 True 时，要求输入可以被 json.dumps，输入仅记录 kwargs
        :param record_output: 是否记录被装饰函数的输出为 span 的 attribution
            当为 True 时，要求输出可以被 json.dumps
        """

        def add_trace_span_wrapper(func):
            @wraps(func)
            async def add_trace_span_inner(*args, **kwargs):
                tracer = trace.get_tracer(__name__)
                span_name_value = span_name if span_name else func.__name__
                if span_name_value.startswith("__"):
                    span_name_value = span_name_value[2:]
                elif span_name_value.startswith("_"):
                    span_name_value = span_name_value[1:]
                attributes_value = {}
                if attributes is not None:
                    attributes_value = {
                        f"{span_name_value}.{k}": attributes[k] for k in attributes
                    }
                if record_input:
                    attributes_value["func_input"] = json.dumps(
                        kwargs, cls=CustomJSONEncoder
                    )
                with tracer.start_as_current_span(
                    span_name_value,
                    context=(
                        TraceContextTextMapPropagator().extract(carrier)
                        if carrier is not None
                        else None
                    ),
                    attributes=attributes_value if attributes_value else None,
                ) as span:
                    res = await func(*args, **kwargs)
                    if record_output:
                        span.add_event(
                            name="func_output",
                            attributes={
                                "name": "func_output",
                                "data": json.dumps(res, cls=CustomJSONEncoder),
                            },
                        )
                return res

            return add_trace_span_inner

        return add_trace_span_wrapper

    def generate_traceparent(self) -> str:
        """
        随机生成 traceparent，例如 00-6688363df60b8e3b80a48219010de3e2-bd02215e6cd47747-01

        :return: traceparent
        """
        trace_id = secrets.token_hex(16)
        span_id = secrets.token_hex(8)
        # 01 表示采样
        # 00 表示不采样
        flags = "01"
        return f"00-{trace_id}-{span_id}-{flags}"

    def get_and_inject_span_context(self, carrier: dict | None = None) -> dict:
        """
        向指定的字典 carrier 注入带有上下文信息的字段

        :param carrier: 指定的字典，如果为 None 时会初始化一个空字典来被注入
        :return: 注入带有上下文信息的字段后的 carrier，当入参的 carrier 不为 None 时，二者是同一个值
        """
        if carrier is None:
            carrier = {}
        TraceContextTextMapPropagator().inject(carrier)
        return carrier

    def get_data_from_context(self, key: str) -> str:
        """
        从上下文信息中获取数据

        :param key: 上下文信息中的 key 值
        :return: 上下文信息中 key 值对应的 value
        """
        res = ""
        if key == "sandbox_action_id":
            res = self.sandbox_action_id.get()
        elif key == "sandbox_step_id":
            res = self.sandbox_step_id.get()
        elif key == "sandbox_sub_session_id":
            res = self.sandbox_sub_session_id.get()
        else:
            logger.warning(
                "get context failed",
                extra={
                    "customized_data_info": {
                        "key": key,
                    }
                },
            )
        return res

    async def get_request_body(self, request: Request) -> tuple[Any | str, str]:
        """
        读取 request 的 body
        """
        body = ""
        message = "get body success"
        try:
            body = await request.json()
        except Exception as e:  # pylint: disable=W0718
            message = (
                f"get body failed with http method {request.method} due to error {e}"
            )
        return body, message

    def get_span_id(self) -> str:
        """
        获取 span id

        :return: span id
        """
        current_span = trace.get_current_span()
        span_id = current_span.get_span_context().span_id
        return trace.format_span_id(span_id)

    def get_trace_id(self, mock_fake_trace_id: bool = False) -> str:
        """
        获取 trace id

        :param mock_fake_trace_id: 当 trace id 未初始化设置时，是否需要 mock 一个假的 trace id
        :return: trace id
        """
        current_span = trace.get_current_span()
        trace_id = current_span.get_span_context().trace_id
        res = trace.format_trace_id(trace_id)
        if mock_fake_trace_id and res == self.zero_trace_id:
            res += str(time_util.get_millisecond_timestamp_of_current_time())
            res += common_util.get_random_id(8)
            res = res[-32:]
        return res

    def set_data_into_context(self, key: str, value: str) -> bool:
        """
        设置上下文信息中的数据

        :param key: 上下文信息中的 key 值
        :param value: 上下文信息中 key 值对应的 value
        :return: 是否设置成功
        """
        res = True
        if key == "sandbox_action_id":
            self.sandbox_action_id.set(value)
        elif key == "sandbox_step_id":
            self.sandbox_step_id.set(value)
        elif key == "sandbox_sub_session_id":
            self.sandbox_sub_session_id.set(value)
        else:
            res = False
            logger.warning(
                "set context failed",
                extra={
                    "customized_data_info": {
                        "key": key,
                        "value": value,
                    }
                },
            )
        return res


class TracedThreadPoolExecutor(ThreadPoolExecutor):
    """
    带有 trace 信息的线程池
    Implementation of :class:`ThreadPoolExecutor` that will pass context into sub tasks.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def submit(self, fn, /, *args, **kwargs):
        # get the current context
        context_for_otel = otel_context.get_current()
        context_for_vars = contextvars.copy_context()
        return super().submit(
            lambda: self.__with_context(
                context_for_otel, context_for_vars, lambda: fn(*args, **kwargs)
            ),
        )

    def __with_context(
        self,
        context_for_otel: otel_context.Context,
        context_for_vars: contextvars.Context,
        fn: Callable,
    ):
        otel_context.attach(context_for_otel)
        return context_for_vars.run(fn)


context_util = ContextUtil()

if __name__ == "__main__":
    logger.info(context_util.get_trace_id(mock_fake_trace_id=True))
    logger.info(context_util.get_and_inject_span_context())
    logger.info(context_util.generate_traceparent())

    import time

    def __test_func() -> str:
        context_util.set_data_into_context("sandbox_action_id", "222")
        logger.info(
            "__test_func thread",
            extra={
                "customized_data_info": {
                    "thread_id": threading.current_thread().ident,
                    "context": context_util.get_data_from_context("sandbox_action_id"),
                }
            },
        )
        return "__test_func finished"

    context_util.set_data_into_context("sandbox_action_id", "cxin")
    fu = TracedThreadPoolExecutor(max_workers=1).submit(__test_func)
    logger.info(fu.result())
    time.sleep(1)
    logger.info(
        "main thread",
        extra={
            "customized_data_info": {
                "thread_id": threading.current_thread().ident,
                "context": context_util.get_data_from_context("sandbox_action_id"),
            }
        },
    )
