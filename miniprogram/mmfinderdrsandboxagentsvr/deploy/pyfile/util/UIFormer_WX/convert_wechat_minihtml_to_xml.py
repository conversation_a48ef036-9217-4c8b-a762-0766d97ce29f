#!/usr/bin/env python3
"""
convert_wechat_minihtml_to_xml.py
---------------------------------
Clean WeChat-mini-program HTML → UIAutomator-style XML.

运行：
    python convert_wechat_minihtml_to_xml.py raw.html   # 输出 hierarchy.xml
若不传文件则从 stdin 读取。
"""

import re
import sys
import xml.etree.ElementTree as ET
from html.parser import HTMLParser
from pathlib import Path

# ──────────────────────────────────────────────────────────────────────────
# 1. 预清洗：把 "wx-viewid=…" → "<wx-view id=…>"；修复缺空格等
# ──────────────────────────────────────────────────────────────────────────


def sanitize_html(raw: str) -> str:
    txt = raw
    # <wx-viewid="16"…> → <wx-view id="16" …>
    txt = txt.replace("id=", " id=")
    # 给闭引号后强制补空格，防止粘连如 "event""id"
    txt = txt.replace("event=", " event=")

    pattern = r'([a-zA-Z0-9_\-]+)="([^"]*)"([a-zA-Z0-9_\-]+)='
    replacement = r'\1="\2" \3='
    txt = re.sub(pattern, replacement, txt)

    # txt = txt.replace("/>", " />")

    return txt


# ──────────────────────────────────────────────────────────────────────────
# 2. 事件到可访问性属性的映射
# ──────────────────────────────────────────────────────────────────────────

EVENT_MAP = {
    "tap": ("clickable", "true"),
    "longtap": ("long-clickable", "true"),
    "longpress": ("long-clickable", "true"),
    "scroll": ("scrollable", "true"),
    "touchmove": ("scrollable", "true"),
    "focus": ("focusable", "true"),
    "focused": ("focused", "true"),
    "input": ("typeable", "true"),
    "checked": ("checked", "true"),
    "checkable": ("checkable", "true"),
    "click": ("clickable", "true"),
}

# ──────────────────────────────────────────────────────────────────────────
# 3. HTML 解析 ➜ 中间树（同时计算 xpath）
# ──────────────────────────────────────────────────────────────────────────


class WxParser(HTMLParser):
    """自定义解析器：构造自定义树结构，同时为每个节点生成唯一 xpath"""

    def __init__(self):
        super().__init__()
        self.stack = []  # 当前父栈（每项是中间节点 dict）
        self.root = {"tag": "hierarchy", "attrs": {"rotation": "0"}, "children": []}
        self.global_idx = 0  # 递增 index，用于 debug

    # 默认属性模板
    def _default_attrs(self, tag: str, resource_id: str | None) -> dict:
        return {
            "index": "0",  # 稍后用全局 idx 填充
            "class": f"{tag}",
            "origin_class": "",
            "resource-id": resource_id or "",
            "package": "",
            "content-desc": "",
            "checkable": "false",
            "checked": "false",
            "clickable": "false",
            "typeable": "false",
            "enabled": "true",
            "focusable": "false",
            "focused": "false",
            "scrollable": "false",
            "long-clickable": "false",
            "password": "false",
            "selected": "false",
            "visible-to-user": "true",
            "bounds": "[0,0][0,0]",
            "drawing-order": "0",
            "hint": "",
            "display-id": "0",
            "text": "",
        }

    # 当前栈顶节点（父亲）
    def _cur(self):
        return self.stack[-1] if self.stack else self.root

    def handle_starttag(self, tag, attrs):
        # print(tag, len(self.stack))
        # print(tag, attrs)
        self.global_idx += 1
        raw_attrs = dict(attrs)
        parent = self._cur()

        same_tag_count = sum(1 for c in parent["children"] if c.get("orig_tag") == tag)
        index_in_parent = same_tag_count + 1

        # 生成 xpath：stack 中节点已经包含 orig_tag & index_in_parent
        xpath_parts = [f"{n['orig_tag']}" for n in self.stack]
        xpath_parts.append(f"{tag}")
        xpath = "/" + "/".join(xpath_parts)

        # 组装属性
        node_attrs = self._default_attrs(tag, raw_attrs.get("id"))
        node_attrs["index"] = str(self.global_idx)
        node_attrs["xpath"] = xpath
        # 复制 id/event/hint 等原生属性，保留信息
        for k, v in raw_attrs.items():
            if k == "class":
                k = "origin_class"
            node_attrs[k] = v
            if v is None:
                print(k, v)
        # 根据 event 映射可点击/可滚动等
        if "event" in raw_attrs:
            events = re.findall(r"[\w-]+", raw_attrs["event"])
            for e in events:
                if e in EVENT_MAP:
                    k, v = EVENT_MAP[e]
                    node_attrs[k] = v
        if "listener" in raw_attrs:
            listeners = re.findall(r"[\w\-$]+", raw_attrs["listener"])
            for e in listeners:
                if e in EVENT_MAP:
                    k, v = EVENT_MAP[e]
                    node_attrs[k] = v

        # 构造中间节点
        node = {
            "tag": "node",
            "orig_tag": tag,
            "index_in_parent": index_in_parent,
            "attrs": node_attrs,
            "children": [],
        }
        parent["children"].append(node)

        self.stack.append(node)

    def handle_endtag(self, _tag):
        # print(self.stack[-1])
        # if not _tag == self.stack[-1]["orig_tag"]:
        #     print([stk["orig_tag"] for stk in self.stack])
        #     exit(0)
        # print(_tag,self.stack[-1]["orig_tag"])
        # while not _tag == self.stack[-1]["orig_tag"]:
        #     print(_tag, self.stack[-1]["orig_tag"])
        #     self.stack.pop()
        if self.stack:
            self.stack.pop()

    def handle_data(self, data):
        if data and not data.isspace():
            cur = self._cur()
            # 只保留首段非空文本到 text 属性
            if not cur["attrs"].get("text"):
                cur["attrs"]["text"] = data.strip()


# ──────────────────────────────────────────────────────────────────────────
# 4. 将中间 dict 树转换为 ElementTree.Element
# ──────────────────────────────────────────────────────────────────────────


def build_et(node_dict):
    el = ET.Element(node_dict["tag"], node_dict["attrs"])
    for child in node_dict["children"]:
        el.append(build_et(child))
    return el


# ──────────────────────────────────────────────────────────────────────────
# 5. 顶层转换函数
# ──────────────────────────────────────────────────────────────────────────


def convert(html: str) -> str:
    parser = WxParser()
    # xml = sanitize_html(html)
    # print(xml.find("/>"))
    # print(xml[xml.find("/>")-40:xml.find("/>")+10])
    # exit(0)
    parser.feed(html)
    et_root = build_et(parser.root)
    ET.indent(et_root, space="  ")  # Python 3.9+
    return ET.tostring(
        et_root, encoding="utf-8", xml_declaration=True, short_empty_elements=True
    ).decode()


# ──────────────────────────────────────────────────────────────────────────
# 6. CLI
# ──────────────────────────────────────────────────────────────────────────


if __name__ == "__main__":
    raw_html = (
        Path(sys.argv[1]).read_text("utf-8") if len(sys.argv) > 1 else sys.stdin.read()
    )
    xml_text = convert(raw_html)
    Path("hierarchy.xml").write_text(xml_text, "utf-8")
    print("✅  hierarchy.xml 生成成功; 每个 <node> 均含 xpath 属性。")
