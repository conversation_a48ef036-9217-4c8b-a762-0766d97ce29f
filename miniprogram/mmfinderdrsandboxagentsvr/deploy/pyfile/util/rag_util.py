"""
rag 工具类
"""

import json

from wxms.logger import logger
from wxms.util.httpx_util import httpx_util


class RagUtil:
    """
    rag 工具类
    """

    def __init__(self, rag_base_url: str):
        self.xpath_summary_url = f"{rag_base_url}/annotations/tasks/xpath_summary"
        self.batch_xpath_summary_url = (
            f"{rag_base_url}/annotations/tasks/batch_xpath_summary"
        )
        self.parse_page_api_url = f"{rag_base_url}/infer/parse_page"
        self.headers = {
            "Content-Type": "application/json",
        }

    def batch_get_xpath_summary_ttlkv(
        self, app_id: str, xpaths: list[str]
    ) -> dict[str, str]:
        """
        从 ttlkv 里获取 xpath 的 summary（批量获取）
        """
        try:
            payload = {
                "app_id": app_id,
                "xpaths": xpaths,
            }
            response = httpx_util.send_request(
                method="POST",
                url=self.batch_xpath_summary_url,
                data=json.dumps(payload),
                headers=self.headers,
                span_name="SendBatchGetXpathSummary",
            )
            response.raise_for_status()
            response_data = response.json()
            logger.info(
                "batch_get_xpath_summary_ttlkv响应",
                extra={
                    "customized_data_info": {
                        "response_result": response_data.get("data", {})
                    }
                },
            )
            return response_data.get("data", {})
        except Exception as e:  # pylint: disable=W0718
            logger.error(
                "batch_get_xpath_summary_ttlkv失败",
                extra={
                    "customized_data_info": {
                        "error": str(e),
                    }
                },
            )
            return {}

    def get_xpath_summary_ttlkv(self, app_id: str, xpath: str) -> str:
        """
        从 ttlkv 里获取 xpath 的 summary
        """
        try:
            payload = {
                "app_id": app_id,
                "xpath": xpath,
            }
            response = httpx_util.send_request(
                method="POST",
                url=self.xpath_summary_url,
                data=json.dumps(payload),
                headers=self.headers,
                span_name="SendGetXpathSummary",
            )
            response.raise_for_status()
            response_data = response.json()
            logger.info(
                "get_xpath_summary_ttlkv响应",
                extra={
                    "customized_data_info": {
                        "response_result": response_data.get("data", "")
                    }
                },
            )
            return response_data.get("data", "")
        except Exception as e:  # pylint: disable=W0718
            logger.error(
                "get_xpath_summary_ttlkv失败",
                extra={
                    "customized_data_info": {
                        "error": str(e),
                    }
                },
            )
            return ""

    def parse_page_api(
        self,
        app_id: str,
        parser_url: str,
        dom_xml: str,
        elements_rects: dict,
        native_elements_rects: dict,
        screenshot_url: str,
        page_url: str,
        rag_extra_config: str,
    ) -> dict:
        """
        远程调用parse_page API
        """
        try:
            payload = {
                "dom_xml": dom_xml,
                "app_id": app_id,
                "parser_url": parser_url,
                "elements_rects": elements_rects,
                "native_elements_rects": native_elements_rects,
                "screen_url": screenshot_url,
                "page_url": page_url,
            }
            # 解析extra_config并添加到payload
            if rag_extra_config:
                try:
                    extra_config = json.loads(rag_extra_config)
                    payload["extra_config"] = extra_config
                except Exception:
                    logger.error("parse_page_api extra_config failed")
            response = httpx_util.send_request(
                method="POST",
                url=self.parse_page_api_url,
                data=json.dumps(payload),
                headers=self.headers,
                span_name="SendParsePage",
            )
            response.raise_for_status()
            result = response.json()
            logger.info(
                "parse_page_api响应",
                extra={"customized_data_info": {"response_result": result}},
            )
            return result
        except Exception as e:  # pylint: disable=W0718
            logger.error(
                "parse_page_api失败", extra={"customized_data_info": {"error": str(e)}}
            )
            return {}


if __name__ == "__main__":
    rag_util = RagUtil(rag_base_url="http://mmfinderdrannotationsvr.polaris:8080")
    TEST_APP_ID = "wxaf35009675aa0b2a"
    TEST_XPATH = "/html/body/wx-view[3]/wx-view[5]"
    value = rag_util.get_xpath_summary_ttlkv(TEST_APP_ID, TEST_XPATH)
    print(value)

    test_xpaths = [
        "/html/body/wx-movable-area/wx-movable-view/wx-view/wx-view/wx-view[2]/wx-view[3]/wx-view/wx-didi-location/wx-view/wx-view[2]",  # pylint: disable=C0301
        "/html/body/wx-movable-area/wx-movable-view/wx-view/wx-view/wx-view[2]/wx-view[3]/wx-view/wx-didi-location/wx-view/wx-view",  # pylint: disable=C0301
    ]
    value = rag_util.batch_get_xpath_summary_ttlkv(TEST_APP_ID, test_xpaths)
    print(value)
