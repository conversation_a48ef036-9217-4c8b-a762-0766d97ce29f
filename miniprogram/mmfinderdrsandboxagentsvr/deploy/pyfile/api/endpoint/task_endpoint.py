"""
task 相关的 api
"""

from fastapi import APIRouter
from model import PythonInput, PythonResult
from task_handler import execute_task
from wxms.model.api.common import BaseHttpResponse

router = APIRouter(tags=["task"], prefix="/task")


@router.post("", summary="新增沙箱推理任务")
def add_task(
    request: PythonInput,
) -> BaseHttpResponse[PythonResult]:
    """
    新增沙箱推理任务
    """
    res = execute_task(request)
    return BaseHttpResponse.make_response(data=res)
