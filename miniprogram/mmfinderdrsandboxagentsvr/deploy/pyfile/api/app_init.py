"""
api 初始化
"""

from api.endpoint.task_endpoint import router as task_router
from fastapi import FastAP<PERSON>
from wxms.config.config import rainbow_config
from wxms.service.clickhouse_service import clickhouse_service
from wxms.util.fastapi.fastapi_util import fastapi_util

app = FastAPI(
    lifespan=fastapi_util.get_lifespan(
        customized_fn_for_start=rainbow_config.init_config_watcher,
        customized_fn_for_end=clickhouse_service.flush,
    )
)
# ============================ middleware ==============================
fastapi_util.init_middleware(app)
# ============================ exception handler =======================
fastapi_util.init_exception_handler(app)
# ============================ common endpoint =========================
fastapi_util.init_router_common(app, prefix="/v1")
app.include_router(task_router, prefix="/v1")
