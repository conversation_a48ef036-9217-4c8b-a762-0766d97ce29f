"""
启动 http server 的主入口
"""

if __name__ == "__main__":
    import os

    import uvicorn
    import wxms
    from wxms.env import CONF_ENV
    from wxms.logger import log_config

    reload_config = {}
    if CONF_ENV != "online":
        reload_config = {
            "reload": True,
            "reload_dirs": [".", "./sdk", os.path.dirname(wxms.__file__)],
        }
    uvicorn.run(
        "api.app_init:app",
        host="0.0.0.0",
        port=8080,
        log_config=log_config,
        log_level="info",
        **reload_config
    )
