"""
基于 xpath 游走
"""

import json
import traceback

from model import <PERSON>I<PERSON><PERSON>, PythonResult, SandboxAgentBase
from wxms.logger import logger
from wxms.middleware.llm import LlmModel
from wxms.model.service.sandbox_service import SandboxActionStandardOutput
from wxms.service.sandbox_service import SandboxClient
from wxms.util.common_util import common_util


class SandboxAgent(SandboxAgentBase):
    """
    基于 xpath 游走的沙箱 Agent 推理类
    """

    def __init__(
        self,
        python_input: PythonInput,
        python_result: PythonResult,
        sandbox_client: SandboxClient,
    ):
        super().__init__(python_input, python_result, sandbox_client)
        # 初始化所有需要跨步保存的状态
        self.xpath_list = (
            self.python_input.special_str_1.split(",")
            if self.python_input.special_str_1
            else []
        )
        self.path_url_list = (
            self.python_input.special_str_2.split(",")
            if self.python_input.special_str_2
            else []
        )
        self.current_xpath_index = -1  # 当前 XPath 索引，独立于 step_count
        self.use_model_times = 0
        self.max_use_model_times = 10
        self.max_step = len(self.xpath_list) + self.max_use_model_times

    def run_one_step_internal(self) -> bool:
        # 1. 构建当前页面 xpath 到框的映射
        last_step_standard_output = self.sandbox_client.standard_output_list[-1]
        xpath_to_rect_map = self.__build_xpath_to_rect_map(
            last_step_standard_output.elements_rects,
            last_step_standard_output.native_elements_rects,
        )
        # 2. 从上报的 xpath_list 中寻找是否有匹配的 xpath
        matched_xpath = None
        target_text = ""
        temp_tuple = self.__get_matched_xpath_info_by_xpath_list(xpath_to_rect_map)
        if temp_tuple is not None:
            # 2.1 如果有匹配的 xpath 则执行操作
            matched_xpath, target_xpath, target_text = temp_tuple
            if target_text:
                if target_text.startswith("scroll"):
                    action = "scroll"
                    self.sandbox_client.standard_scroll(
                        coordinate=(
                            int(target_text.split(" ")[1]),
                            int(target_text.split(" ")[2]),
                        ),
                        delta_x=0 - int(target_text.split(" ")[4]),
                        delta_y=0 - int(target_text.split(" ")[3]),
                    )
                else:
                    action = "input"
                    self.sandbox_client.standard_input(
                        xpath=target_xpath, text=target_text
                    )
            else:
                action = "click"
                self.sandbox_client.standard_click(xpath=target_xpath)
        else:
            # 2.2 没有匹配的则模型辅助点击
            # 由于模型辅助有次数限制，且模型的输出结果不一定可用，所以这里存在 matched_xpath 为 None 的情况
            action = "click"
            matched_xpath = self.__get_matched_xpath_by_model(xpath_to_rect_map)
            if matched_xpath is not None:
                self.sandbox_client.standard_click(xpath=matched_xpath)
        # 3. 根据是否有 matched_xpath 来判断是否要继续下一步骤
        if matched_xpath is not None:
            # 有 matched_xpath 则必定有操作
            new_step_standard_output = self.sandbox_client.standard_output_list[-1]
            matched_rect = xpath_to_rect_map[matched_xpath]
            self.__log_step(
                before_step_standard_output=last_step_standard_output,
                action=action,
                after_step_standard_output=new_step_standard_output,
                matched_xpath=matched_xpath,
                matched_rect=matched_rect,
                target_text=target_text,
            )
            return False
        else:
            return True

    def __build_xpath_to_rect_map(
        self, elements_rects: dict, native_elements_rects: dict
    ) -> dict[str, list[int]]:
        result: dict[str, list[int]] = {}
        value_list = elements_rects.get("data", {}).get("result", {}).get(
            "result", {}
        ).get("value", []) + native_elements_rects.get("data", {}).get(
            "result", {}
        ).get(
            "result", {}
        ).get(
            "value", []
        )
        for item in value_list:
            xpath = item.get("xpath")
            rect = item.get("rect")
            if xpath and rect:
                x = rect["x"]
                y = rect["y"]
                width = rect["width"]
                height = rect["height"]
                x1 = x + width
                y1 = y + height
                result[xpath] = [int(x), int(y), int(x1), int(y1)]
        return result

    def __get_action_answer_content(self) -> str:
        base_url = self.python_input.vlt_base_url_v1
        model_name = self.python_input.vlt_model_name_v1
        prompt = """
你是一个小程序操作助手，上述图像是按顺序操作任务指令，最近一张是当前图像。当前指令是「关闭广告弹窗 或者 完成一键登录流程 或者选择最近地址店铺 或者同意协议并领取乘车码」，请问在当前图像里我要怎么操作？

当前只能有一种输出选项：只能回复接下来的一个动作，不能回复多个动作，并给出在页面的精确像素坐标（x y），最终输出格式严格遵循<think>xxx</think><answer>xxx</answer>这个格式。answer动作只能在以下1类集合选>择：
1. click x y(代表点击像素坐标 x,y)

思考内容：首先判断当前应采取的正确的动作，例如 该界面需要点击同意，应该采取点击位置click x1 y1实现指令；该界面没有弹窗或者登录界面，应该认为是finish完成；当前界面是空白界面，直接finish。

注意：

# 弹窗处理：
1.发现弹窗元素（包含“同意协议”、“授权”、“温馨提示”等）→ 点击核心操作按钮（优先级）：
- 协议弹窗：点击"同意"或"确认"
- 广告弹窗：点击关闭按钮（通常位于弹窗的右上方或者弹窗的正下方）
2.弹窗遮挡核心功能（如登录按钮）→ 先关闭弹窗
3.如果该弹窗事实上是登录流程的一部分（如“同意协议”），则直接点击“同意”或“确认”按钮；或者是手机号登录，则直接点击“手机快速验证”按钮。
4.如果是允许获取您的位置信息 → 点击暂不按钮

# 登录流程：
1.存在未勾选的协议复选框 → 点击勾选框
2.显示手机快速验证按钮 → 直接点击该功能
3.出现密码输入框 → 先处理前置弹窗（按▢规则）
4.出现开卡领优惠并且选中同意协议按钮 → 直接点击该功能
5.已经选中同意协议并且显示手机号一键登录 → 点击手机号一键登录按钮

# 地址店铺选择：
1. 存在待选择地址店铺 → 点击最近的地址店铺
2. 已经选择最近的地址店铺 → 点击下单按钮

# 任务终止条件：
1.界面无任何弹窗元素
2.已显示登录成功状态（如用户头像）
3.存在无法操作的非弹窗元素（如空白页）
4.连续3个以上界面毫无变化
5.界面没有地址店铺选择

# 禁止行为
× 输出多个动作指令
× 操作小程序关闭/后退按钮
× 猜测坐标（需确保元素在截图中可见）"""
        answer_content = ""
        try:
            llm_model = LlmModel(base_url=base_url, api_key="EMPTY")
            img_dto = self.sandbox_client.standard_output_list[-1].screenshot_dto
            input_imgs_content = [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{common_util.encode_image(img_dto.file)}",
                        "customized_url_for_trace": img_dto.url,
                    },
                }
            ]
            messages = [
                {
                    "role": "user",
                    "content": input_imgs_content
                    + [
                        {
                            "type": "text",
                            "text": prompt,
                        }
                    ],
                }
            ]
            answer_content, _ = llm_model.chat_completion(
                model=model_name,
                messages=messages,
                temperature=0.0,
                seed=2025,
            )
        except Exception as e:  # pylint: disable=W0718
            logger.error(
                "__get_action_answer_content failed",
                extra={
                    "customized_data_info": {
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }
                },
            )
        return answer_content

    def __get_matched_xpath_by_model(
        self, xpath_to_rect_map: dict[str, list[int]]
    ) -> None | str:
        res = None
        while self.use_model_times < self.max_use_model_times:
            self.use_model_times += 1
            answer_content = self.__get_action_answer_content()
            action_7b = common_util.extract_xml_tag(
                content=answer_content, tag="answer"
            )
            action_7b = action_7b.replace("\n", "").strip()
            if action_7b.startswith("click"):
                parts = action_7b.replace(",", " ").split()
                if len(parts) != 3:
                    continue
                x, y = int(parts[1]), int(parts[2])
                # 根据模型推理出来的操作找到最合适的框
                best_xpath = ""
                min_area = float("inf")
                for xpath, rect in xpath_to_rect_map.items():
                    x1, y1, x2, y2 = rect
                    # 检查点击坐标是否在 rect 内
                    if (x1 <= x <= x2) and (y1 <= y <= y2):
                        area = (x2 - x1) * (y2 - y1)
                        # 选择面积最小的元素
                        if area < min_area:
                            min_area = area
                            best_xpath = xpath
                if best_xpath:
                    res = best_xpath
                    break
        return res

    def __get_matched_xpath_by_target_xpath(
        self, xpath_to_rect_map: dict[str, list[int]], target_xpath: str
    ) -> str | None:
        res = None
        if target_xpath in xpath_to_rect_map:
            res = target_xpath
        else:
            area = None
            for xpath, rect in xpath_to_rect_map.items():
                if xpath.startswith(target_xpath):
                    # 计算面积 (width * height)
                    width = rect[2] - rect[0]  # x2 - x1
                    height = rect[3] - rect[1]  # y2 - y1
                    temp_area = width * height
                    if area is None or temp_area < area:
                        area = temp_area
                        res = xpath
        return res

    def __get_matched_xpath_info_by_xpath_list(
        self, xpath_to_rect_map: dict[str, list[int]]
    ) -> tuple[str, str, str] | None:
        res = None
        temp_xpath_index = self.current_xpath_index + 1
        while temp_xpath_index < len(self.xpath_list):
            if len(self.xpath_list[temp_xpath_index].split("|")) == 2:
                target_xpath = self.xpath_list[temp_xpath_index].split("|")[0]
                target_text = self.xpath_list[temp_xpath_index].split("|")[1]
            else:
                target_xpath = self.xpath_list[temp_xpath_index]
                target_text = ""
            # if path != current_path_url 执行模型操作 暂空
            matched_xpath = self.__get_matched_xpath_by_target_xpath(
                xpath_to_rect_map, target_xpath
            )
            if matched_xpath is not None:
                res = (matched_xpath, target_xpath, target_text)
                self.current_xpath_index = temp_xpath_index
                break
            else:
                temp_xpath_index += 1
        return res

    def __log_step(
        self,
        before_step_standard_output: SandboxActionStandardOutput,
        action: str,
        after_step_standard_output: SandboxActionStandardOutput,
        matched_xpath: str,
        matched_rect: list,
        target_text: str,
    ):
        data = {
            "before_image": before_step_standard_output.screenshot_dto.url,
            "before_path_url": before_step_standard_output.applet_page_info.get(
                "data", {}
            )
            .get("data", {})
            .get("path", ""),
            "action": action,
            "after_image": after_step_standard_output.screenshot_dto.url,
            "after_path_url": after_step_standard_output.applet_page_info.get(
                "data", {}
            )
            .get("data", {})
            .get("path", ""),
            "matched_xpath": matched_xpath,
            "matched_rect": matched_rect,
            "target_text": target_text,
            "task_id": self.sandbox_client.request_id,
        }
        self.python_result.answers.append(json.dumps(data, ensure_ascii=False))
