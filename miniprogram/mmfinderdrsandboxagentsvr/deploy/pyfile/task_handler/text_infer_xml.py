"""
xml 推理逻辑
"""

import json
import re
import traceback
from copy import deepcopy

import openai
from jinja2 import Template
from model import <PERSON>Input, PythonResult, SandboxAgentBase
from pydantic import BaseModel
from util.rag_util import RagUtil
from wxms.logger import logger
from wxms.model.service.sandbox_service import (
    SandboxActionMessage,
    SandboxActionMessageStyleType,
    SandboxActionStandardConfig,
    SandboxActionStandardOutput,
)
from wxms.service.sandbox_service import SandboxClient

text_pattern = re.compile(r"(text:\s*)([^;]*)(;)")
idx_pattern = re.compile(r"^\s*\[\s*(\d+)\s*\]")
resource_id_pattern = re.compile(r"resource_id: .*?; ")

tools = [
    {
        "type": "function",
        "function": {
            "name": "click",
            "description": "点击对应的元素",
            "parameters": {
                "type": "object",
                "properties": {
                    "id": {"type": "integer", "description": "需要点击的元素id"},
                    "time": {"type": "integer", "description": "点击次数，默认为1"},
                    "summary": {"type": "string", "description": "点击动作的摘要信息，示例：点击同意按钮"},
                },
                "required": ["id"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "type",
            "description": "在对应元素中输入内容，输入前会自动触发一次click操作",
            "parameters": {
                "type": "object",
                "properties": {
                    "id": {"type": "integer", "description": "元素id"},
                    "content": {"type": "string", "description": "输入的内容"},
                    "summary": {"type": "string", "description": "输入动作的摘要信息，示例：点击输入框并输入内容"},
                },
                "required": ["id", "content"],
            },
        },
    },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "scroll",
    #         "description": "滑动屏幕",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "id": {"type": "integer", "description": "元素id"},
    #                 "direction": {
    #                     "type": "string",
    #                     "enum": ["up", "down", "left", "right"],
    #                     "description": "向指定的的方向滑动屏幕，比如down代表将屏幕向下滑动，展示屏幕上方的隐藏内容",
    #                 },
    #             },
    #             "required": ["direction"],
    #         },
    #     },
    # },
    {
        "type": "function",
        "function": {
            "name": "back",
            "description": "返回上一个页面",
            "parameters": {"type": "object", "properties": {}},
        },
    },
    {
        "type": "function",
        "function": {
            "name": "wait",
            "description": "等待",
            "parameters": {
                "type": "object",
                "properties": {
                    "seconds": {"type": "integer", "description": "等待时间"},
                },
                "required": ["seconds"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "terminate",
            "description": "结束小程序操作",
            "parameters": {
                "type": "object",
                "properties": {
                    "state": {
                        "type": "string",
                        "enum": ["success", "failure"],
                        "description": "任务完成状态",
                    },
                    "final_answer": {
                        "type": "string",
                        "description": "展示给用户的最终答复",
                    },
                },
                "required": ["state"],
            },
        },
    },
]


class XMLInfoDTO(BaseModel):
    """
    parse_page 的结果
    """

    xml_data: str
    idx_to_xpath: dict[int, str]
    xpath_to_idx: dict[str, int]
    screen: str
    original_screen: str
    log_screen: str
    idx_to_rect: dict[int, dict]


def parse_action(message: dict) -> list[dict]:
    """
    解析大模型返回的操作
    """
    tool_calls = message["tool_calls"] if message.get("tool_calls") else []
    actions = []
    for tool_call in tool_calls:
        function = tool_call["function"]
        name = function["name"]
        args = json.loads(function["arguments"])
        actions.append(
            {"name": name, "args": args}
        )
    return actions


def parse_page(
    last_step_standard_output: SandboxActionStandardOutput,
    app_id: str,
    parser_url: str,
    rag_base_url: str,
    rag_extra_config: str,
) -> XMLInfoDTO:
    """
    调用远程API解析页面，返回XMLInfoDTO对象
    """
    try:
        xml_data: str = (
            last_step_standard_output.dom_xml.get("data", {})
            .get("result", {})
            .get("result", {})
            .get("value", "")
        )
        elements_rects: dict = (
            last_step_standard_output.elements_rects.get("data", {})
            .get("result", {})
            .get("result", {})
            .get("value", {})
        )
        native_elements_rects: dict = (
            last_step_standard_output.native_elements_rects.get("data", {})
            .get("result", {})
            .get("result", {})
            .get("value", {})
        )
        screenshot_url: str = last_step_standard_output.screenshot_dto.url
        full_path = (
            output.applet_page_info.get("data", {}).get("data", {}).get("full_path", "")
        )
        if full_path and (not full_path.startswith("/")):
            full_path = "/" + full_path
        result = RagUtil(rag_base_url=rag_base_url).parse_page_api(
            app_id,
            parser_url,
            xml_data,
            elements_rects,
            native_elements_rects,
            screenshot_url,
            full_path,
            rag_extra_config,
        )
        xml_info = result.get("data", {})
        return XMLInfoDTO(**xml_info)
    except Exception as e:  # pylint: disable=W0718
        logger.error(
            "parse_page failed",
            extra={
                "customized_data_info": {
                    "exception": str(e),
                    "traceback": traceback.format_exc(),
                }
            },
        )
        return XMLInfoDTO(
            xml_data="",
            idx_to_xpath={},
            xpath_to_idx={},
            screen="",
            original_screen="",
            log_screen="",
            idx_to_rect={},
        )


def execute_action(
    sandbox_client: SandboxClient, action: dict, idx_to_xpath: dict
) -> str:
    """
    执行沙箱中的操作
    """
    name = action["name"]
    args = action["args"]

    # 执行操作
    if name == "wait":
        # 构建沙箱操作的标准配置
        sandbox_action_standard_config = SandboxActionStandardConfig(
            action_message_list_before_action=(
                [
                    SandboxActionMessage(
                        style_type=SandboxActionMessageStyleType.SUBTITLE,
                        content="等待页面加载",
                    )
                ]
            ),
        )
        sandbox_client.standard_wait(
            seconds=args["seconds"], config=sandbox_action_standard_config
        )
        return "success"
    elif name == "back":
        # 构建沙箱操作的标准配置
        sandbox_action_standard_config = SandboxActionStandardConfig(
            action_message_list_before_action=(
                [
                    SandboxActionMessage(
                        style_type=SandboxActionMessageStyleType.SUBTITLE,
                        content="返回上一页面",
                    )
                ]
            ),
        )
        sandbox_client.standard_navigate_back(
            back_num=1, config=sandbox_action_standard_config
        )
        return "success"
    xpath = idx_to_xpath.get(args["id"])
    if not xpath:
        return "无效的元素id"
    if name == "scroll":
        delta_x = delta_y = 0
        direction = args.get("direction")
        if direction == "up":
            delta_y = -400
        elif direction == "down":
            delta_y = 400
        elif direction == "left":
            delta_x = -300
        elif direction == "right":
            delta_x = 300
        # 构建沙箱操作的标准配置
        sandbox_action_standard_config = SandboxActionStandardConfig(
            action_message_list_before_action=(
                [
                    SandboxActionMessage(
                        style_type=SandboxActionMessageStyleType.SUBTITLE,
                        content=args.get("summary"),
                    ) if args.get("summary") else None
                ]
            ),
        )
        sandbox_client.standard_scroll(
            xpath=xpath,
            delta_x=delta_x,
            delta_y=delta_y,
            config=sandbox_action_standard_config,
        )
    elif name == "click":
        # 构建沙箱操作的标准配置
        sandbox_action_standard_config = SandboxActionStandardConfig(
            action_message_list_before_action=(
                [
                    SandboxActionMessage(
                        style_type=SandboxActionMessageStyleType.SUBTITLE,
                        content=args.get("summary"),
                    ) if args.get("summary") else None
                ]
            ),
        )
        click_times = args.get("time", 1)
        sandbox_client.standard_click(
            xpath=xpath, click_times=click_times, config=sandbox_action_standard_config
        )
    elif name == "type":
        # 构建沙箱操作的标准配置
        sandbox_action_standard_config = SandboxActionStandardConfig(
            action_message_list_before_action=(
                [
                    SandboxActionMessage(
                        style_type=SandboxActionMessageStyleType.SUBTITLE,
                        content=args.get("summary"),
                    ) if args.get("summary") else None
                ]
            ),
        )
        content = args["content"]
        sandbox_client.standard_input(
            text=content, xpath=xpath, config=sandbox_action_standard_config
        )
    return "success"


def to_json(obj) -> str:
    """
    获取 json 字符串
    """
    return json.dumps(obj, ensure_ascii=False)


def get_click_boxs(action, idx_to_rect):
    """
    根据 action 和 idx_to_rect 提取 click_boxs
    """
    click_boxs = {}
    action_id = action.get("args", {}).get("id", None)
    if (
        action_id is not None
        and action_id in idx_to_rect
        and isinstance(idx_to_rect[action_id], dict)
    ):
        click_boxs = idx_to_rect[action_id]
    return click_boxs


class SandboxAgent(SandboxAgentBase):
    """
    xml 推理逻辑的沙箱 Agent 推理类
    """

    def __init__(
        self,
        python_input: PythonInput,
        python_result: PythonResult,
        sandbox_client: SandboxClient,
    ):
        super().__init__(python_input, python_result, sandbox_client)
        # 初始化所有需要跨步保存的状态
        self.client = openai.OpenAI(
            base_url=python_input.base_url,
            api_key="EMPTY",
        )
        self.xml_info = XMLInfoDTO(
            xml_data="",
            idx_to_xpath={},
            xpath_to_idx={},
            screen="",
            original_screen="",
            log_screen="",
            idx_to_rect={},
        )
        self.messages = []

    def init_step_internal(self):
        last_step_standard_output = self.sandbox_client.standard_output_list[-1]
        self.xml_info = parse_page(
            last_step_standard_output,
            self.python_input.app_id,
            self.python_input.vlt_base_url,
            self.python_input.rag_base_url,
            self.python_input.rag_extra_config,
        )
        self.python_result.screens.append(self.xml_info.log_screen)
        template = Template(
            "<user_input>\n{{instruction}}\n</user_input>"
            + "\n\n"
            + "<state_0>\n{{state}}\n</state_0>"
        )
        self.messages = [
            {"role": "system", "content": self.python_input.prompt},
            {
                "role": "user",
                "content": template.render(
                    instruction=self.python_input.instruction,
                    state=self.xml_info.screen,
                ),
            },
        ]

    def run_one_step_internal(self) -> bool:
        res = False
        try:
            # 1. inference
            if "no_think" in self.python_input.model_name:
                self.messages[-1]["content"] += " /no_think"
                gen_args = {
                    "temperature": 0.6,
                    "top_p": 0.8,
                    "extra_body": {"top_k": 20, "min_p": 0.0},
                }
            else:
                gen_args = {
                    "temperature": 0.7,
                    "top_p": 0.95,
                    "extra_body": {"top_k": 20, "min_p": 0.0},
                }
            completion = self.client.chat.completions.create(
                model=self.python_input.model_name.replace("_no_think", ""),
                messages=self.messages,
                stream=False,
                max_tokens=1024,
                tool_choice="auto",
                tools=tools,  # type: ignore
                **gen_args,
            )
            assistant_message = completion.choices[0].message.model_dump()
            self.python_result.answers_raw_data.append(assistant_message["content"])
            actions = parse_action(assistant_message)
            self.messages.append(assistant_message)
            # 2. action & observation
            for action in actions:
                self.python_result.answers.append(to_json(action))
                # 2.1. trace log data
                self.python_result.log_data.append(
                    {
                        "click_boxs": get_click_boxs(action, self.xml_info.idx_to_rect),
                        "idx_to_xpath": self.xml_info.idx_to_xpath,
                        "idx_to_rect": self.xml_info.idx_to_rect,
                        "invalid_ids": list(
                            set(self.xml_info.idx_to_xpath.keys())
                            - set(self.xml_info.idx_to_rect.keys())
                        ),  # 没有位置信息的xpath id
                        "messages": self.messages[:],
                    }
                )
                # 2.2. action
                if action.get("name") == "terminate":
                    res = True
                    break
                state = execute_action(
                    self.sandbox_client, action, self.xml_info.idx_to_xpath
                )
                if state == "success":
                    new_step_standard_output = self.sandbox_client.standard_output_list[
                        -1
                    ]
                    self.xml_info = parse_page(
                        new_step_standard_output,
                        self.python_input.app_id,
                        self.python_input.vlt_base_url,
                        self.python_input.rag_base_url,
                        self.python_input.rag_extra_config,
                    )
                    self.python_result.screens.append(self.xml_info.log_screen)
                    index = self.step_count + 1
                    self.messages.append(
                        {
                            "role": "tool",
                            "content": f"<state_{index}>\n{self.xml_info.screen}\n</state_{index}>",
                        }
                    )
                else:
                    # 对于模型幻觉的情况，可能出现“无效的元素id”，需要将上一步的 screen 和 screenshot 添加到 debug 信息中
                    # 从而让其数量和 answers 的数量一致
                    last_step_standard_output = deepcopy(
                        self.sandbox_client.standard_output_list[-1]
                    )
                    self.sandbox_client.standard_output_list.append(
                        last_step_standard_output
                    )
                    self.python_result.screens.append(self.xml_info.log_screen)
                    index = self.step_count + 1
                    self.messages.append(
                        {
                            "role": "tool",
                            "content": f"<state_{index}>\n{state}\n</state_{index}>\n",
                        }
                    )
        except Exception as e:
            self.python_result.answers.append(
                to_json(
                    {
                        "name": "terminate",
                        "args": {
                            "state": "failure",
                            "final_answer": "操作失败，程序意外退出",
                        },
                    }
                )
            )
            self.python_result.log_data.append(
                {
                    "messages": self.messages[:],
                }
            )
            raise e
        # 如果已经是最后一轮且操作还没结束
        if self.step_count + 1 == self.max_step and not res:
            self.python_result.answers.append(
                to_json(
                    {
                        "name": "terminate",
                        "args": {
                            "state": "failure",
                            "final_answer": "操作失败，已达到最大操作次数",
                        },
                    }
                )
            )
            self.python_result.log_data.append(
                {
                    "messages": self.messages[:],
                }
            )
        return res
