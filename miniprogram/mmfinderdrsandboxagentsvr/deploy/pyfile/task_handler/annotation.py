"""
沙箱标注任务处理
"""

import json

from model import SandboxAgentBase
from wxms.logger import logger


class SandboxAgent(SandboxAgentBase):
    """
    沙箱标注任务处理的沙箱 Agent 推理类
    """

    def run_one_step_internal(self) -> bool:
        action = json.loads(self.python_input.special_str_1)
        action_type = action.get("type", "")
        logger.info(
            "start annotation action",
            extra={
                "customized_data_info": {
                    "action": action,
                    "action_type": action_type,
                }
            },
        )
        if action_type == "click":
            xpath = action.get("xpath", "")
            if xpath:
                self.sandbox_client.standard_click(xpath=xpath)
            else:
                x = action.get("x", 0)
                y = action.get("y", 0)
                self.sandbox_client.standard_click(coordinate=(x, y))
        elif action_type == "search":
            xpath = action.get("xpath", "")
            text = action.get("text", "")
            if xpath:
                self.sandbox_client.standard_input(text=text, xpath=xpath)
            else:
                x = action.get("x", 0)
                y = action.get("y", 0)
                self.sandbox_client.standard_input(text=text, coordinate=(x, y))
        elif action_type == "back":
            self.sandbox_client.standard_navigate_back(back_num=1)
        elif action_type == "scroll":
            xpath = action.get("xpath", "")
            delta_x = action.get("delta_x", 0)
            delta_y = action.get("delta_y", 0)
            if xpath:
                self.sandbox_client.standard_scroll(
                    xpath=xpath,
                    delta_x=delta_x,
                    delta_y=delta_y,
                )
            else:
                x = action.get("x", 0)
                y = action.get("y", 0)
                self.sandbox_client.standard_scroll(
                    coordinate=(x, y),
                    delta_x=delta_x,
                    delta_y=delta_y,
                )
        elif action_type == "wait":
            seconds = action.get("seconds", 3)
            self.sandbox_client.standard_wait(seconds=seconds)
        elif action_type in ("launch_applet", "terminate"):
            pass
        else:
            logger.warning(
                "unexpected action_type",
                extra={"customized_data_info": {"action": action}},
            )
        logger.info(
            "end annotation action",
            extra={
                "customized_data_info": {
                    "action": action,
                    "action_type": action_type,
                }
            },
        )
        return True
