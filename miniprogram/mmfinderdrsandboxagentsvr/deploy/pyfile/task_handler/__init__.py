"""
自动注册各个推理逻辑的 task handler
"""

import importlib.util
import json
import os
import traceback
from typing import Optional, Type

from func_timeout.exceptions import FunctionTimedOut
from model import PythonInput, PythonResult, PythonResultStatus, SandboxAgentBase
from wxms.env import (
    CONF_ENV,
    MODULE,
    WXMS_BASE_URL,
)
from wxms.logger import logger
from wxms.model.exception.error_code_exception import AppletInterruptError
from wxms.model.service.sandbox_service import (
    SandboxActionMessage,
    SandboxActionMessageStyleType,
    SandboxActionStandardConfig,
)
from wxms.service.clickhouse_service import SubSession, clickhouse_service
from wxms.service.sandbox_service import SandboxClient
from wxms.util.common_util import CustomJSONEncoder, common_util
from wxms.util.context_util import context_util
from wxms.util.time_util import time_util

REGISTRY = {}


def execute_task(python_input: PythonInput) -> PythonResult:
    """
    执行 task
    """
    python_result = PythonResult(
        answers=[],
        answers_raw_data=[],
        interrupt={},
        log_data=[],
        long_img_url="",
        long_unique_img_url="",
        screens=[],
        standard_output_list=[],
        status=PythonResultStatus.SUCCESS,
        target_id="",
    )
    carrier = None
    if context_util.get_trace_id() == context_util.zero_trace_id:
        carrier = {
            "traceparent": context_util.generate_traceparent(),
            "tracestate": "appid=mmfinderdrsandbox",
        }
        logger.info(
            "get trace id failed, use mocked trace",
            extra={
                "customized_data_info": {
                    "carrier": carrier,
                },
            },
        )

    @context_util.add_trace_span(
        span_name=python_input.run_mode,
        carrier=carrier,
        attributes=python_input.model_dump(),
    )
    def __inner():
        started_at = time_util.get_millisecond_timestamp_of_current_time()
        context_util.set_data_into_context(
            "sandbox_sub_session_id", context_util.get_span_id()
        )
        error_dict = {}
        if agent_cls := get_task_agent(python_input.run_mode):
            sandbox_client = SandboxClient(
                namespace=python_input.namespace,
                base_url=WXMS_BASE_URL,
                app_id=python_input.app_id,
                app_entry_url=python_input.app_entry_url,
                uin=int(python_input.uin),
                from_username=python_input.from_username,
                username=python_input.username,
                headless_mode=int(python_input.headless_mode),
                standard_start_config=__get_standard_start_config(
                    python_input=python_input,
                ),
                check_screenshot_loading_status_max_retry_count=int(
                    python_input.sandbox_check_screenshot_loading_status_max_retry_count
                ),
                check_screenshot_loading_status_max_retry_count_for_back=int(
                    python_input.sandbox_check_screenshot_loading_status_max_retry_count_for_back
                ),
                is_async_action_result=python_input.sandbox_is_async_action_result
                == "1",
                link_type=python_input.sandbox_link_type,
                skip_launch_applet=python_input.sandbox_skip_launch_applet == "1",
                sleep_seconds_before_screenshot=float(
                    python_input.sandbox_sleep_seconds_before_screenshot
                ),
            )
            python_result.target_id = sandbox_client.target_id
            python_result.standard_output_list = sandbox_client.standard_output_list
            if sandbox_client.start_interrupt is not None:
                python_result.interrupt = sandbox_client.start_interrupt
            if sandbox_client.stop_flag:
                # 启动的时候出现问题则直接退出
                return
            # 开始逐步执行操作
            try:
                agent_obj = agent_cls(
                    python_input=python_input,
                    python_result=python_result,
                    sandbox_client=sandbox_client,
                )
                agent_obj.init_step()
                time_util.execute_function_with_timeout(540, agent_obj.run)
            except AppletInterruptError as e:
                python_result.interrupt = e.params
            except (FunctionTimedOut, Exception) as e:  # pylint: disable=W0718
                python_result.status = PythonResultStatus.FAILED
                error_dict = {
                    "exception": str(e),
                    "traceback": traceback.format_exc(),
                }
                logger.error(
                    "execute task failed",
                    extra={
                        "customized_data_info": error_dict,
                    },
                )
            finally:
                sandbox_client.standard_end(
                    close_applet=python_input.sandbox_close_applet == "1",
                    keep_session=python_input.sandbox_close_applet_with_session_alive
                    == "1",
                    config=SandboxActionStandardConfig(
                        action_message_list_before_action=(
                            [
                                SandboxActionMessage(
                                    style_type=SandboxActionMessageStyleType.TITLE,
                                    content="总结操作结果",
                                )
                            ]
                            if python_result.interrupt.get("error_code", 0) != -9
                            and python_input.run_mode != "just_launch_applet"
                            else None
                        )
                    ),
                )
                python_result.long_img_url = sandbox_client.long_img_url
                python_result.long_unique_img_url = sandbox_client.long_unique_img_url
                first_standard_output = sandbox_client.standard_output_list[0]
                device_info = (
                    first_standard_output.applet_page_info.get("data", {})
                    .get("data", {})
                    .get("device_info", {})
                )
                result_dict = python_result.model_dump()
                for key, value in error_dict.items():
                    result_dict[key] = value
                clickhouse_service.add_sub_session(
                    SubSession(
                        started_at=started_at,
                        ended_at=time_util.get_millisecond_timestamp_of_current_time(),
                        module=MODULE,
                        environment=CONF_ENV,
                        namespace=python_input.namespace,
                        session_id=context_util.get_trace_id(),
                        sub_session_id=context_util.get_data_from_context(
                            "sandbox_sub_session_id"
                        ),
                        uin=int(python_input.uin),
                        app_id=python_input.app_id,
                        device_memory_size=device_info.get("memory_size", -1),
                        device_model=device_info.get("model", ""),
                        device_system=device_info.get("system", ""),
                        device_real_width=first_standard_output.screenshot_dto.real_size[
                            0
                        ],
                        device_real_height=first_standard_output.screenshot_dto.real_size[
                            1
                        ],
                        device_resized_width=first_standard_output.screenshot_dto.resized_size[
                            0
                        ],
                        device_resized_height=first_standard_output.screenshot_dto.resized_size[
                            1
                        ],
                        parameter=python_input.model_dump_json(),
                        from_username=python_input.from_username,
                        headless_mode=int(python_input.headless_mode),
                        run_mode=python_input.run_mode,
                        result=json.dumps(result_dict, cls=CustomJSONEncoder),
                        interrupt=json.dumps(
                            python_result.interrupt, cls=CustomJSONEncoder
                        ),
                        status=python_result.status.value,
                    )
                )
        else:
            raise ValueError(f"wrong run_mode {python_input.run_mode}")

    __inner()
    return python_result


def get_task_agent(task_type: str) -> Optional[Type[SandboxAgentBase]]:
    """
    获取 task agent 函数
    """
    return REGISTRY.get(task_type)


def __get_standard_start_config(
    python_input: PythonInput,
) -> SandboxActionStandardConfig:
    app_link = f'<a data-miniprogram-isagenttextstyle="1" data-miniprogram-appid="{common_util.base64_encode(python_input.app_id)}" href=" ">{python_input.app_name}</a>'  # pylint: disable=C0301
    action_message_list = [
        SandboxActionMessage(
            style_type=SandboxActionMessageStyleType.TITLE,
            content=(
                f"正在使用{app_link}小程序{python_input.raw_query}"
                if python_input.run_mode != "just_launch_applet"
                else f"正在打开{app_link}小程序"
            ),
        ),
    ]
    return SandboxActionStandardConfig(
        action_message_list_before_action=action_message_list
    )


for i in os.walk(os.path.dirname(__file__)):
    folder, x, files = i
    for file in files:
        if not file.endswith(".py") or file in ["__init__.py"]:
            continue
        module_name = file.split(".")[0]
        file_path = os.path.join(folder, file)
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        if spec is None or spec.loader is None:
            continue
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        REGISTRY[file[:-3]] = module.SandboxAgent
